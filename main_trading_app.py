"""
تطبيق التداول الرئيسي مع واجهة المستخدم
يدمج جميع مكونات استراتيجية السكالبينغ الاحترافية
"""

import asyncio
import logging
import json
import os
from datetime import datetime
from typing import Dict, List, Optional
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import queue

from advanced_quotex_integration import AdvancedQuotexIntegration

class TradingApp:
    """تطبيق التداول الرئيسي"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 استراتيجية السكالبينغ الاحترافية - PyQuotex Advanced")
        self.root.geometry("1200x800")
        self.root.configure(bg='#1e1e1e')
        
        # متغيرات التطبيق
        self.integration = None
        self.is_connected = False
        self.is_trading = False
        self.log_queue = queue.Queue()
        
        # إعداد التسجيل
        self.setup_logging()
        
        # إنشاء واجهة المستخدم
        self.create_ui()
        
        # بدء معالج الرسائل
        self.root.after(100, self.process_log_queue)
        
    def setup_logging(self):
        """إعداد نظام التسجيل"""
        # إنشاء مجلد السجلات
        os.makedirs("logs", exist_ok=True)
        
        # إعداد التسجيل
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f"logs/trading_{datetime.now().strftime('%Y%m%d')}.log"),
                QueueHandler(self.log_queue)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        # إعداد الألوان والخطوط
        bg_color = '#1e1e1e'
        fg_color = '#ffffff'
        accent_color = '#00ff88'
        button_color = '#2d2d2d'
        
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # إعداد الشبكة
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=2)
        main_frame.rowconfigure(1, weight=1)
        
        # قسم الاتصال والإعدادات
        self.create_connection_section(main_frame)
        
        # قسم المراقبة والإحصائيات
        self.create_monitoring_section(main_frame)
        
        # قسم السجلات
        self.create_logs_section(main_frame)
        
        # قسم التحكم
        self.create_control_section(main_frame)
    
    def create_connection_section(self, parent):
        """إنشاء قسم الاتصال"""
        # إطار الاتصال
        conn_frame = ttk.LabelFrame(parent, text="🔗 الاتصال والإعدادات", padding=10)
        conn_frame.grid(row=0, column=0, columnspan=2, sticky="ew", pady=(0, 10))
        
        # بيانات الحساب
        ttk.Label(conn_frame, text="البريد الإلكتروني:").grid(row=0, column=0, sticky="w", padx=(0, 10))
        self.email_var = tk.StringVar()
        self.email_entry = ttk.Entry(conn_frame, textvariable=self.email_var, width=30)
        self.email_entry.grid(row=0, column=1, sticky="w", padx=(0, 20))
        
        ttk.Label(conn_frame, text="كلمة المرور:").grid(row=0, column=2, sticky="w", padx=(0, 10))
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(conn_frame, textvariable=self.password_var, show="*", width=30)
        self.password_entry.grid(row=0, column=3, sticky="w")
        
        # أزرار التحكم
        self.connect_btn = ttk.Button(conn_frame, text="🔌 اتصال", command=self.connect_to_quotex)
        self.connect_btn.grid(row=1, column=0, pady=(10, 0), sticky="w")
        
        self.disconnect_btn = ttk.Button(conn_frame, text="🔌 قطع الاتصال", command=self.disconnect_from_quotex, state="disabled")
        self.disconnect_btn.grid(row=1, column=1, pady=(10, 0), sticky="w")
        
        # حالة الاتصال
        self.connection_status = ttk.Label(conn_frame, text="❌ غير متصل", foreground="red")
        self.connection_status.grid(row=1, column=2, pady=(10, 0), sticky="w")
        
        # إعدادات التداول
        settings_frame = ttk.LabelFrame(conn_frame, text="⚙️ إعدادات التداول", padding=5)
        settings_frame.grid(row=2, column=0, columnspan=4, sticky="ew", pady=(10, 0))
        
        ttk.Label(settings_frame, text="المبلغ الافتراضي:").grid(row=0, column=0, sticky="w")
        self.amount_var = tk.StringVar(value="10")
        ttk.Entry(settings_frame, textvariable=self.amount_var, width=10).grid(row=0, column=1, sticky="w", padx=(5, 20))
        
        ttk.Label(settings_frame, text="الحد الأقصى للصفقات:").grid(row=0, column=2, sticky="w")
        self.max_trades_var = tk.StringVar(value="3")
        ttk.Entry(settings_frame, textvariable=self.max_trades_var, width=10).grid(row=0, column=3, sticky="w", padx=(5, 0))
    
    def create_monitoring_section(self, parent):
        """إنشاء قسم المراقبة"""
        # إطار المراقبة
        monitor_frame = ttk.LabelFrame(parent, text="📊 المراقبة والإحصائيات", padding=10)
        monitor_frame.grid(row=1, column=0, sticky="nsew", padx=(0, 5))
        
        # معلومات الحساب
        account_frame = ttk.LabelFrame(monitor_frame, text="💰 معلومات الحساب", padding=5)
        account_frame.pack(fill="x", pady=(0, 10))
        
        self.balance_label = ttk.Label(account_frame, text="الرصيد: $0.00", font=("Arial", 12, "bold"))
        self.balance_label.pack(anchor="w")
        
        self.daily_profit_label = ttk.Label(account_frame, text="الربح اليومي: $0.00")
        self.daily_profit_label.pack(anchor="w")
        
        self.daily_trades_label = ttk.Label(account_frame, text="الصفقات اليومية: 0")
        self.daily_trades_label.pack(anchor="w")
        
        # إحصائيات الأداء
        performance_frame = ttk.LabelFrame(monitor_frame, text="📈 إحصائيات الأداء", padding=5)
        performance_frame.pack(fill="x", pady=(0, 10))
        
        self.win_rate_label = ttk.Label(performance_frame, text="معدل النجاح: 0%")
        self.win_rate_label.pack(anchor="w")
        
        self.total_trades_label = ttk.Label(performance_frame, text="إجمالي الصفقات: 0")
        self.total_trades_label.pack(anchor="w")
        
        self.total_profit_label = ttk.Label(performance_frame, text="إجمالي الربح: $0.00")
        self.total_profit_label.pack(anchor="w")
        
        # الصفقات النشطة
        active_frame = ttk.LabelFrame(monitor_frame, text="🔄 الصفقات النشطة", padding=5)
        active_frame.pack(fill="both", expand=True)
        
        # جدول الصفقات النشطة
        columns = ("الأصل", "الاتجاه", "المبلغ", "الوقت المتبقي")
        self.trades_tree = ttk.Treeview(active_frame, columns=columns, show="headings", height=6)
        
        for col in columns:
            self.trades_tree.heading(col, text=col)
            self.trades_tree.column(col, width=80)
        
        scrollbar = ttk.Scrollbar(active_frame, orient="vertical", command=self.trades_tree.yview)
        self.trades_tree.configure(yscrollcommand=scrollbar.set)
        
        self.trades_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_logs_section(self, parent):
        """إنشاء قسم السجلات"""
        # إطار السجلات
        logs_frame = ttk.LabelFrame(parent, text="📝 سجل الأحداث", padding=10)
        logs_frame.grid(row=1, column=1, sticky="nsew", padx=(5, 0))
        
        # منطقة النص للسجلات
        self.logs_text = scrolledtext.ScrolledText(
            logs_frame, 
            wrap=tk.WORD, 
            width=60, 
            height=20,
            bg='#2d2d2d',
            fg='#ffffff',
            font=("Consolas", 9)
        )
        self.logs_text.pack(fill="both", expand=True)
        
        # أزرار السجلات
        logs_buttons_frame = ttk.Frame(logs_frame)
        logs_buttons_frame.pack(fill="x", pady=(10, 0))
        
        ttk.Button(logs_buttons_frame, text="🗑️ مسح السجل", command=self.clear_logs).pack(side="left")
        ttk.Button(logs_buttons_frame, text="💾 حفظ السجل", command=self.save_logs).pack(side="left", padx=(10, 0))
    
    def create_control_section(self, parent):
        """إنشاء قسم التحكم"""
        # إطار التحكم
        control_frame = ttk.LabelFrame(parent, text="🎮 التحكم في التداول", padding=10)
        control_frame.grid(row=2, column=0, columnspan=2, sticky="ew", pady=(10, 0))
        
        # أزرار التداول
        self.start_trading_btn = ttk.Button(
            control_frame, 
            text="🚀 بدء التداول الآلي", 
            command=self.start_trading,
            state="disabled"
        )
        self.start_trading_btn.pack(side="left", padx=(0, 10))
        
        self.stop_trading_btn = ttk.Button(
            control_frame, 
            text="⏹️ إيقاف التداول", 
            command=self.stop_trading,
            state="disabled"
        )
        self.stop_trading_btn.pack(side="left", padx=(0, 10))
        
        # حالة التداول
        self.trading_status = ttk.Label(control_frame, text="⏸️ التداول متوقف", foreground="orange")
        self.trading_status.pack(side="left", padx=(20, 0))
        
        # اختيار الأصول
        assets_frame = ttk.LabelFrame(control_frame, text="📈 الأصول المختارة", padding=5)
        assets_frame.pack(side="right", fill="y")
        
        self.selected_assets = tk.StringVar(value="EURUSD,GBPUSD,USDJPY")
        ttk.Entry(assets_frame, textvariable=self.selected_assets, width=30).pack()
    
    def connect_to_quotex(self):
        """الاتصال بـ Quotex"""
        email = self.email_var.get().strip()
        password = self.password_var.get().strip()
        
        if not email or not password:
            messagebox.showerror("خطأ", "يرجى إدخال البريد الإلكتروني وكلمة المرور")
            return
        
        # تشغيل الاتصال في خيط منفصل
        threading.Thread(target=self._connect_async, args=(email, password), daemon=True).start()
        
        # تعطيل زر الاتصال
        self.connect_btn.config(state="disabled", text="🔄 جاري الاتصال...")
    
    def _connect_async(self, email: str, password: str):
        """الاتصال غير المتزامن"""
        try:
            # إنشاء حلقة أحداث جديدة للخيط
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # إنشاء التكامل
            self.integration = AdvancedQuotexIntegration(email, password)
            
            # محاولة الاتصال
            success = loop.run_until_complete(self.integration.initialize())
            
            if success:
                self.is_connected = True
                self.logger.info("✅ تم الاتصال بنجاح مع Quotex")
                
                # تحديث واجهة المستخدم
                self.root.after(0, self._update_connection_ui, True)
                
                # بدء مراقبة الحساب
                self.root.after(0, self.start_account_monitoring)
                
            else:
                self.logger.error("❌ فشل الاتصال مع Quotex")
                self.root.after(0, self._update_connection_ui, False)
                
        except Exception as e:
            self.logger.error(f"❌ خطأ في الاتصال: {e}")
            self.root.after(0, self._update_connection_ui, False)
    
    def _update_connection_ui(self, connected: bool):
        """تحديث واجهة المستخدم للاتصال"""
        if connected:
            self.connection_status.config(text="✅ متصل", foreground="green")
            self.connect_btn.config(state="disabled", text="🔌 اتصال")
            self.disconnect_btn.config(state="normal")
            self.start_trading_btn.config(state="normal")
        else:
            self.connection_status.config(text="❌ غير متصل", foreground="red")
            self.connect_btn.config(state="normal", text="🔌 اتصال")
            self.disconnect_btn.config(state="disabled")
            self.start_trading_btn.config(state="disabled")
    
    def disconnect_from_quotex(self):
        """قطع الاتصال من Quotex"""
        if self.integration:
            threading.Thread(target=self._disconnect_async, daemon=True).start()
    
    def _disconnect_async(self):
        """قطع الاتصال غير المتزامن"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            loop.run_until_complete(self.integration.disconnect())
            self.is_connected = False
            self.is_trading = False
            
            self.root.after(0, self._update_connection_ui, False)
            self.root.after(0, self._update_trading_ui, False)
            
            self.logger.info("✅ تم قطع الاتصال بنجاح")
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في قطع الاتصال: {e}")
    
    def start_trading(self):
        """بدء التداول الآلي"""
        if not self.is_connected:
            messagebox.showerror("خطأ", "يجب الاتصال أولاً")
            return
        
        # الحصول على الأصول المختارة
        assets_str = self.selected_assets.get().strip()
        assets = [asset.strip() for asset in assets_str.split(",") if asset.strip()]
        
        if not assets:
            messagebox.showerror("خطأ", "يرجى اختيار أصول للتداول")
            return
        
        # تحديث الإعدادات
        try:
            amount = float(self.amount_var.get())
            max_trades = int(self.max_trades_var.get())
            
            self.integration.trading_config['default_amount'] = amount
            self.integration.trading_config['max_concurrent_trades'] = max_trades
            
        except ValueError:
            messagebox.showerror("خطأ", "قيم الإعدادات غير صحيحة")
            return
        
        # بدء التداول
        threading.Thread(target=self._start_trading_async, args=(assets,), daemon=True).start()
        
        self.is_trading = True
        self._update_trading_ui(True)
        self.logger.info(f"🚀 بدء التداول الآلي للأصول: {assets}")
    
    def _start_trading_async(self, assets: List[str]):
        """بدء التداول غير المتزامن"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            loop.run_until_complete(self.integration.start_trading(assets))
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في التداول: {e}")
            self.root.after(0, self._update_trading_ui, False)
    
    def stop_trading(self):
        """إيقاف التداول"""
        if self.integration and self.is_trading:
            threading.Thread(target=self._stop_trading_async, daemon=True).start()
    
    def _stop_trading_async(self):
        """إيقاف التداول غير المتزامن"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            loop.run_until_complete(self.integration.stop_trading())
            
            self.is_trading = False
            self.root.after(0, self._update_trading_ui, False)
            self.logger.info("⏹️ تم إيقاف التداول")
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في إيقاف التداول: {e}")
    
    def _update_trading_ui(self, trading: bool):
        """تحديث واجهة المستخدم للتداول"""
        if trading:
            self.trading_status.config(text="🟢 التداول نشط", foreground="green")
            self.start_trading_btn.config(state="disabled")
            self.stop_trading_btn.config(state="normal")
        else:
            self.trading_status.config(text="⏸️ التداول متوقف", foreground="orange")
            self.start_trading_btn.config(state="normal" if self.is_connected else "disabled")
            self.stop_trading_btn.config(state="disabled")

    def start_account_monitoring(self):
        """بدء مراقبة الحساب"""
        self.update_account_info()
        # تحديث كل 10 ثوانٍ
        self.root.after(10000, self.start_account_monitoring)

    def update_account_info(self):
        """تحديث معلومات الحساب"""
        if self.integration and self.is_connected:
            try:
                summary = self.integration.get_trading_summary()

                # تحديث معلومات الحساب
                trading_state = summary.get('trading_state', {})
                self.balance_label.config(text=f"الرصيد: ${trading_state.get('balance', 0):.2f}")
                self.daily_profit_label.config(text=f"الربح اليومي: ${trading_state.get('daily_profit', 0):.2f}")
                self.daily_trades_label.config(text=f"الصفقات اليومية: {trading_state.get('daily_trades', 0)}")

                # تحديث إحصائيات الأداء
                performance = summary.get('performance_metrics', {})
                win_rate = performance.get('win_rate', 0) * 100
                self.win_rate_label.config(text=f"معدل النجاح: {win_rate:.1f}%")
                self.total_trades_label.config(text=f"إجمالي الصفقات: {performance.get('total_trades', 0)}")
                self.total_profit_label.config(text=f"إجمالي الربح: ${performance.get('total_profit', 0):.2f}")

                # تحديث الصفقات النشطة
                self.update_active_trades(trading_state.get('active_trades', {}))

            except Exception as e:
                self.logger.error(f"❌ خطأ في تحديث معلومات الحساب: {e}")

    def update_active_trades(self, active_trades: Dict):
        """تحديث جدول الصفقات النشطة"""
        # مسح الجدول
        for item in self.trades_tree.get_children():
            self.trades_tree.delete(item)

        # إضافة الصفقات النشطة
        for trade_id, trade_info in active_trades.items():
            asset = trade_info.get('asset', '')
            direction = trade_info.get('direction', '').upper()
            amount = f"${trade_info.get('amount', 0):.2f}"

            # حساب الوقت المتبقي
            expected_end = trade_info.get('expected_end_time')
            if expected_end:
                try:
                    from datetime import datetime
                    if isinstance(expected_end, str):
                        expected_end = datetime.fromisoformat(expected_end.replace('Z', '+00:00'))

                    remaining = expected_end - datetime.now()
                    remaining_seconds = max(0, int(remaining.total_seconds()))
                    remaining_text = f"{remaining_seconds}s"
                except:
                    remaining_text = "غير معروف"
            else:
                remaining_text = "غير معروف"

            self.trades_tree.insert("", "end", values=(asset, direction, amount, remaining_text))

    def process_log_queue(self):
        """معالجة قائمة انتظار السجلات"""
        try:
            while True:
                record = self.log_queue.get_nowait()
                self.add_log_message(record)
        except queue.Empty:
            pass

        # جدولة المعالجة التالية
        self.root.after(100, self.process_log_queue)

    def add_log_message(self, record):
        """إضافة رسالة إلى السجل"""
        try:
            # تنسيق الرسالة
            timestamp = datetime.fromtimestamp(record.created).strftime('%H:%M:%S')
            level = record.levelname
            message = record.getMessage()

            # تحديد لون الرسالة
            if level == 'ERROR':
                color = '#ff6b6b'
            elif level == 'WARNING':
                color = '#ffd93d'
            elif level == 'INFO':
                color = '#6bcf7f'
            else:
                color = '#ffffff'

            # إضافة الرسالة
            formatted_message = f"[{timestamp}] {level}: {message}\n"

            self.logs_text.config(state=tk.NORMAL)
            self.logs_text.insert(tk.END, formatted_message)

            # تطبيق اللون
            start_line = self.logs_text.index(tk.END + "-2l")
            end_line = self.logs_text.index(tk.END + "-1l")
            self.logs_text.tag_add(f"color_{record.created}", start_line, end_line)
            self.logs_text.tag_config(f"color_{record.created}", foreground=color)

            self.logs_text.config(state=tk.DISABLED)
            self.logs_text.see(tk.END)

            # الاحتفاظ بآخر 1000 سطر فقط
            lines = self.logs_text.get("1.0", tk.END).split('\n')
            if len(lines) > 1000:
                self.logs_text.config(state=tk.NORMAL)
                self.logs_text.delete("1.0", f"{len(lines)-1000}.0")
                self.logs_text.config(state=tk.DISABLED)

        except Exception as e:
            print(f"خطأ في إضافة رسالة السجل: {e}")

    def clear_logs(self):
        """مسح السجل"""
        self.logs_text.config(state=tk.NORMAL)
        self.logs_text.delete("1.0", tk.END)
        self.logs_text.config(state=tk.DISABLED)

    def save_logs(self):
        """حفظ السجل"""
        try:
            from tkinter import filedialog

            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                title="حفظ السجل"
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.logs_text.get("1.0", tk.END))

                messagebox.showinfo("نجح", f"تم حفظ السجل في: {filename}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ السجل: {e}")

    def on_closing(self):
        """عند إغلاق التطبيق"""
        if self.is_trading:
            if messagebox.askokcancel("تأكيد", "التداول نشط. هل تريد إيقافه وإغلاق التطبيق؟"):
                if self.integration:
                    threading.Thread(target=self._disconnect_async, daemon=True).start()
                    # انتظار قصير للسماح بإيقاف التداول
                    self.root.after(2000, self.root.destroy)
                else:
                    self.root.destroy()
        else:
            if self.integration:
                threading.Thread(target=self._disconnect_async, daemon=True).start()
                self.root.after(1000, self.root.destroy)
            else:
                self.root.destroy()

    def run(self):
        """تشغيل التطبيق"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()


class QueueHandler(logging.Handler):
    """معالج السجلات للقائمة"""

    def __init__(self, log_queue):
        super().__init__()
        self.log_queue = log_queue

    def emit(self, record):
        self.log_queue.put(record)


def main():
    """الدالة الرئيسية"""
    try:
        # إنشاء وتشغيل التطبيق
        app = TradingApp()
        app.run()

    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
