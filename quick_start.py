#!/usr/bin/env python3
"""
🚀 التشغيل السريع لاستراتيجية السكالبينغ الاحترافية
Quick Start for Professional Scalping Strategy

هذا الملف يوفر طريقة سريعة لبدء التداول مع الإعدادات المُحسنة مسبقاً
"""

import asyncio
import logging
import json
import os
from datetime import datetime
from typing import Dict, List, Optional

# استيراد المكونات الرئيسية
try:
    from advanced_quotex_integration import AdvancedQuotexIntegration
    from professional_scalping_strategy import ProfessionalScalpingStrategy
    print("✅ تم تحميل جميع المكونات بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد المكونات: {e}")
    print("تأكد من تثبيت جميع المتطلبات: pip install -r requirements.txt")
    exit(1)

class QuickStartTrader:
    """مُتداول التشغيل السريع"""
    
    def __init__(self):
        self.config = self.load_config()
        self.setup_logging()
        self.integration = None
        self.is_running = False
        
        print("🎯 تم تهيئة مُتداول التشغيل السريع")
    
    def load_config(self) -> Dict:
        """تحميل الإعدادات"""
        try:
            if os.path.exists('config.json'):
                with open('config.json', 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print("✅ تم تحميل الإعدادات من config.json")
                return config
            else:
                print("⚠️ ملف الإعدادات غير موجود، استخدام الإعدادات الافتراضية")
                return self.get_default_config()
        except Exception as e:
            print(f"❌ خطأ في تحميل الإعدادات: {e}")
            return self.get_default_config()
    
    def get_default_config(self) -> Dict:
        """الإعدادات الافتراضية"""
        return {
            "trading_config": {
                "default_amount": 10,
                "max_concurrent_trades": 3,
                "default_assets": ["EURUSD", "GBPUSD", "USDJPY"]
            }
        }
    
    def setup_logging(self):
        """إعداد نظام التسجيل"""
        # إنشاء مجلد السجلات
        os.makedirs("logs", exist_ok=True)
        
        # إعداد التسجيل
        log_format = '%(asctime)s - %(levelname)s - %(message)s'
        log_file = f"logs/quick_start_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        print(f"📝 تم إعداد نظام التسجيل: {log_file}")
    
    def get_credentials(self) -> tuple:
        """الحصول على بيانات الحساب"""
        print("\n🔐 إدخال بيانات حساب Quotex:")
        print("=" * 50)
        
        email = input("📧 البريد الإلكتروني: ").strip()
        if not email:
            print("❌ البريد الإلكتروني مطلوب")
            return None, None
        
        import getpass
        password = getpass.getpass("🔒 كلمة المرور: ").strip()
        if not password:
            print("❌ كلمة المرور مطلوبة")
            return None, None
        
        return email, password
    
    def get_trading_settings(self) -> Dict:
        """الحصول على إعدادات التداول"""
        print("\n⚙️ إعدادات التداول:")
        print("=" * 50)
        
        # المبلغ الافتراضي
        default_amount = self.config.get("trading_config", {}).get("default_amount", 10)
        amount_input = input(f"💰 المبلغ الافتراضي للصفقة (افتراضي: ${default_amount}): ").strip()
        
        try:
            amount = float(amount_input) if amount_input else default_amount
        except ValueError:
            amount = default_amount
            print(f"⚠️ قيمة غير صحيحة، استخدام الافتراضي: ${default_amount}")
        
        # عدد الصفقات المتزامنة
        default_max_trades = self.config.get("trading_config", {}).get("max_concurrent_trades", 3)
        max_trades_input = input(f"🔢 الحد الأقصى للصفقات المتزامنة (افتراضي: {default_max_trades}): ").strip()
        
        try:
            max_trades = int(max_trades_input) if max_trades_input else default_max_trades
        except ValueError:
            max_trades = default_max_trades
            print(f"⚠️ قيمة غير صحيحة، استخدام الافتراضي: {default_max_trades}")
        
        # الأصول للتداول
        default_assets = self.config.get("trading_config", {}).get("default_assets", ["EURUSD", "GBPUSD", "USDJPY"])
        assets_input = input(f"📈 الأصول للتداول (افتراضي: {','.join(default_assets)}): ").strip()
        
        if assets_input:
            assets = [asset.strip().upper() for asset in assets_input.split(",")]
        else:
            assets = default_assets
        
        return {
            "amount": amount,
            "max_trades": max_trades,
            "assets": assets
        }
    
    async def initialize_trading(self, email: str, password: str, settings: Dict) -> bool:
        """تهيئة التداول"""
        try:
            print("\n🔄 تهيئة التداول...")
            print("=" * 50)
            
            # إنشاء التكامل
            self.integration = AdvancedQuotexIntegration(email, password)
            
            # تطبيق الإعدادات
            self.integration.trading_config.update({
                'default_amount': settings['amount'],
                'max_concurrent_trades': settings['max_trades']
            })
            
            # الاتصال بـ Quotex
            print("🔌 الاتصال بـ Quotex...")
            success = await self.integration.initialize()
            
            if success:
                print("✅ تم الاتصال بنجاح!")
                
                # عرض معلومات الحساب
                summary = self.integration.get_trading_summary()
                balance = summary.get('trading_state', {}).get('balance', 0)
                print(f"💰 الرصيد الحالي: ${balance:.2f}")
                
                return True
            else:
                print("❌ فشل الاتصال بـ Quotex")
                return False
                
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة التداول: {e}")
            print(f"❌ خطأ في التهيئة: {e}")
            return False
    
    async def start_trading(self, assets: List[str]):
        """بدء التداول"""
        try:
            print(f"\n🚀 بدء التداول الآلي...")
            print(f"📈 الأصول: {', '.join(assets)}")
            print("=" * 50)
            
            self.is_running = True
            
            # بدء التداول
            await self.integration.start_trading(assets)
            
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف التداول بواسطة المستخدم")
            await self.stop_trading()
        except Exception as e:
            self.logger.error(f"خطأ في التداول: {e}")
            print(f"❌ خطأ في التداول: {e}")
            await self.stop_trading()
    
    async def stop_trading(self):
        """إيقاف التداول"""
        try:
            if self.integration and self.is_running:
                print("\n⏹️ إيقاف التداول...")
                await self.integration.stop_trading()
                await self.integration.disconnect()
                self.is_running = False
                print("✅ تم إيقاف التداول بنجاح")
                
                # عرض ملخص الأداء
                self.show_performance_summary()
                
        except Exception as e:
            self.logger.error(f"خطأ في إيقاف التداول: {e}")
            print(f"❌ خطأ في الإيقاف: {e}")
    
    def show_performance_summary(self):
        """عرض ملخص الأداء"""
        try:
            if not self.integration:
                return
            
            summary = self.integration.get_trading_summary()
            trading_state = summary.get('trading_state', {})
            performance = summary.get('performance_metrics', {})
            
            print("\n📊 ملخص الأداء:")
            print("=" * 50)
            print(f"💰 الرصيد النهائي: ${trading_state.get('balance', 0):.2f}")
            print(f"📈 الربح اليومي: ${trading_state.get('daily_profit', 0):.2f}")
            print(f"🔢 الصفقات اليومية: {trading_state.get('daily_trades', 0)}")
            print(f"🎯 معدل النجاح: {performance.get('win_rate', 0)*100:.1f}%")
            print(f"📊 إجمالي الصفقات: {performance.get('total_trades', 0)}")
            print(f"💵 إجمالي الربح: ${performance.get('total_profit', 0):.2f}")
            
        except Exception as e:
            print(f"❌ خطأ في عرض الملخص: {e}")
    
    def show_welcome_message(self):
        """عرض رسالة الترحيب"""
        print("\n" + "="*60)
        print("🚀 استراتيجية السكالبينغ الاحترافية - التشغيل السريع")
        print("   Professional Scalping Strategy - Quick Start")
        print("="*60)
        print("📋 المميزات:")
        print("   ✅ 4 طبقات تحليل متقدمة")
        print("   ✅ ذكاء اصطناعي متطور")
        print("   ✅ إدارة مخاطر ذكية")
        print("   ✅ واجهة سهلة الاستخدام")
        print("="*60)
        print("⚠️  تحذير: التداول ينطوي على مخاطر عالية")
        print("   استثمر فقط ما يمكنك تحمل خسارته")
        print("="*60)
    
    async def run(self):
        """تشغيل التداول السريع"""
        try:
            # رسالة الترحيب
            self.show_welcome_message()
            
            # الحصول على بيانات الحساب
            email, password = self.get_credentials()
            if not email or not password:
                print("❌ بيانات الحساب مطلوبة للمتابعة")
                return
            
            # الحصول على إعدادات التداول
            settings = self.get_trading_settings()
            
            # تأكيد البدء
            print(f"\n📋 ملخص الإعدادات:")
            print(f"   💰 المبلغ: ${settings['amount']}")
            print(f"   🔢 الحد الأقصى للصفقات: {settings['max_trades']}")
            print(f"   📈 الأصول: {', '.join(settings['assets'])}")
            
            confirm = input("\n✅ هل تريد بدء التداول؟ (y/n): ").strip().lower()
            if confirm not in ['y', 'yes', 'نعم']:
                print("❌ تم إلغاء التداول")
                return
            
            # تهيئة التداول
            if await self.initialize_trading(email, password, settings):
                # بدء التداول
                await self.start_trading(settings['assets'])
            
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف البرنامج بواسطة المستخدم")
        except Exception as e:
            self.logger.error(f"خطأ في التشغيل: {e}")
            print(f"❌ خطأ في التشغيل: {e}")
        finally:
            if self.is_running:
                await self.stop_trading()


async def main():
    """الدالة الرئيسية"""
    trader = QuickStartTrader()
    await trader.run()


if __name__ == "__main__":
    try:
        # تشغيل التداول السريع
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 وداعاً!")
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()
