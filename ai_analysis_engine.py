"""
محرك الذكاء الاصطناعي المتقدم للسكالبينغ الاحترافي
يتضمن ML Classifier، Feature Engineering، وتدريب النماذج
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.feature_selection import SelectKBest, f_classif
import joblib
import logging
import json
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class AISignal:
    """إشارة الذكاء الاصطناعي"""
    prediction: str
    confidence: float
    probability_distribution: Dict[str, float]
    feature_importance: Dict[str, float]
    model_performance: Dict[str, float]
    details: Dict[str, Any]

class AIAnalysisEngine:
    """محرك الذكاء الاصطناعي المتقدم"""
    
    def __init__(self):
        # نماذج التعلم الآلي
        self.models = {
            'random_forest': RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                class_weight='balanced'
            ),
            'gradient_boosting': GradientBoostingClassifier(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                random_state=42
            )
        }
        
        # معالجات البيانات
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        self.feature_selector = SelectKBest(f_classif, k=20)
        
        # حالة النماذج
        self.models_trained = False
        self.feature_names = []
        self.model_performance = {}
        
        # مجلد حفظ النماذج
        self.models_dir = Path("data/ai_models")
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        # إعدادات التدريب
        self.training_config = {
            'min_samples': 100,
            'test_size': 0.2,
            'cv_folds': 5,
            'feature_threshold': 0.01
        }
        
    def analyze_market(self, candles: List[Dict], indicators: Dict,
                      technical_analysis: Dict = None, quantitative_analysis: Dict = None,
                      behavioral_analysis: Dict = None) -> Dict[str, Any]:
        """التحليل بالذكاء الاصطناعي"""
        try:
            if len(candles) < 30:
                return self._get_insufficient_data_result()
            
            # استخراج الميزات
            features = self._extract_features(candles, indicators, technical_analysis, 
                                            quantitative_analysis, behavioral_analysis)
            
            if not features:
                return self._get_feature_extraction_error()
            
            # التحقق من تدريب النماذج
            if not self.models_trained:
                # محاولة تحميل النماذج المحفوظة
                if not self._load_models():
                    return self._get_untrained_models_result()
            
            # التنبؤ باستخدام النماذج
            predictions = self._make_predictions(features)
            
            # دمج التنبؤات
            ensemble_prediction = self._ensemble_predictions(predictions)
            
            # تحليل أهمية الميزات
            feature_importance = self._analyze_feature_importance(features)
            
            # تقييم جودة التنبؤ
            prediction_quality = self._assess_prediction_quality(predictions, features)
            
            return {
                'signal_type': 'AI_ANALYSIS',
                'prediction': ensemble_prediction['prediction'],
                'confidence': ensemble_prediction['confidence'],
                'probability_distribution': ensemble_prediction['probabilities'],
                'feature_importance': feature_importance,
                'prediction_quality': prediction_quality,
                'individual_predictions': predictions,
                'model_performance': self.model_performance,
                'features_used': len(features),
                'timestamp': pd.Timestamp.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الذكاء الاصطناعي: {e}")
            return self._get_error_result()
    
    def _extract_features(self, candles: List[Dict], indicators: Dict,
                         technical_analysis: Dict = None, quantitative_analysis: Dict = None,
                         behavioral_analysis: Dict = None) -> List[float]:
        """استخراج الميزات للذكاء الاصطناعي"""
        try:
            features = []
            
            # ميزات من الشموع
            candle_features = self._extract_candle_features(candles)
            features.extend(candle_features)
            
            # ميزات من المؤشرات الفنية
            indicator_features = self._extract_indicator_features(indicators)
            features.extend(indicator_features)
            
            # ميزات من التحليل الفني
            if technical_analysis:
                ta_features = self._extract_technical_analysis_features(technical_analysis)
                features.extend(ta_features)
            
            # ميزات من التحليل الكمي
            if quantitative_analysis:
                qa_features = self._extract_quantitative_analysis_features(quantitative_analysis)
                features.extend(qa_features)
            
            # ميزات من التحليل السلوكي
            if behavioral_analysis:
                ba_features = self._extract_behavioral_analysis_features(behavioral_analysis)
                features.extend(ba_features)
            
            # ميزات مشتقة
            derived_features = self._extract_derived_features(candles, indicators)
            features.extend(derived_features)
            
            # تنظيف الميزات
            cleaned_features = self._clean_features(features)
            
            return cleaned_features
            
        except Exception as e:
            logger.error(f"خطأ في استخراج الميزات: {e}")
            return []
    
    def _extract_candle_features(self, candles: List[Dict]) -> List[float]:
        """استخراج ميزات من الشموع"""
        try:
            if len(candles) < 5:
                return []
            
            features = []
            recent_candles = candles[-5:]  # آخر 5 شموع
            
            # ميزات السعر
            prices = [c['close'] for c in recent_candles]
            features.append(np.mean(prices))  # متوسط السعر
            features.append(np.std(prices))   # انحراف السعر
            features.append((prices[-1] - prices[0]) / prices[0])  # تغير السعر النسبي
            
            # ميزات الحجم
            volumes = [c.get('volume', 0) for c in recent_candles]
            if sum(volumes) > 0:
                features.append(np.mean(volumes))
                features.append(np.std(volumes))
                features.append(volumes[-1] / np.mean(volumes[:-1]) if np.mean(volumes[:-1]) > 0 else 1)
            else:
                features.extend([0, 0, 1])
            
            # ميزات الشموع
            for candle in recent_candles[-3:]:  # آخر 3 شموع
                body_size = abs(candle['close'] - candle['open'])
                total_range = candle['high'] - candle['low']
                
                features.append(body_size / total_range if total_range > 0 else 0)  # نسبة الجسم
                features.append((candle['close'] - candle['open']) / candle['open'])  # اتجاه الشمعة
                
                # ذيول الشمعة
                upper_wick = candle['high'] - max(candle['open'], candle['close'])
                lower_wick = min(candle['open'], candle['close']) - candle['low']
                features.append(upper_wick / total_range if total_range > 0 else 0)
                features.append(lower_wick / total_range if total_range > 0 else 0)
            
            return features
            
        except Exception as e:
            logger.error(f"خطأ في استخراج ميزات الشموع: {e}")
            return []
    
    def _extract_indicator_features(self, indicators: Dict) -> List[float]:
        """استخراج ميزات من المؤشرات"""
        try:
            features = []
            
            # مؤشرات RSI
            for period in [5, 14]:
                rsi_key = f"rsi_{period}"
                if rsi_key in indicators:
                    rsi_value = indicators[rsi_key]
                    features.append(rsi_value / 100)  # تطبيع RSI
                    features.append(1 if rsi_value > 70 else (-1 if rsi_value < 30 else 0))  # منطقة RSI
                else:
                    features.extend([0.5, 0])
            
            # مؤشرات EMA
            ema_values = []
            for period in [5, 10, 21]:
                ema_key = f"ema_{period}"
                if ema_key in indicators:
                    ema_values.append(indicators[ema_key])
                    features.append(indicators[ema_key])
                else:
                    features.append(0)
            
            # تقاطعات EMA
            if len(ema_values) >= 2:
                features.append(1 if ema_values[0] > ema_values[1] else -1)  # EMA5 vs EMA10
                if len(ema_values) >= 3:
                    features.append(1 if ema_values[1] > ema_values[2] else -1)  # EMA10 vs EMA21
            else:
                features.extend([0, 0])
            
            # MACD
            if 'macd' in indicators and isinstance(indicators['macd'], dict):
                macd_data = indicators['macd']
                macd_line = macd_data.get('macd', 0)
                signal_line = macd_data.get('signal', 0)
                
                features.append(macd_line)
                features.append(signal_line)
                features.append(1 if macd_line > signal_line else -1)  # تقاطع MACD
                features.append(macd_data.get('histogram', 0))
            else:
                features.extend([0, 0, 0, 0])
            
            # Bollinger Bands
            if 'bollinger_bands' in indicators and isinstance(indicators['bollinger_bands'], dict):
                bb_data = indicators['bollinger_bands']
                upper = bb_data.get('upper', 0)
                lower = bb_data.get('lower', 0)
                middle = bb_data.get('middle', 0)
                
                if upper > lower:
                    bb_width = (upper - lower) / middle if middle > 0 else 0
                    features.append(bb_width)
                    # موقع السعر في النطاق (افتراض أن السعر الحالي هو middle)
                    features.append(0.5)  # موقع وسط
                else:
                    features.extend([0, 0.5])
            else:
                features.extend([0, 0.5])
            
            # مؤشرات إضافية
            for indicator in ['momentum_10', 'zscore_20']:
                if indicator in indicators:
                    value = indicators[indicator]
                    features.append(np.tanh(value))  # تطبيع باستخدام tanh
                else:
                    features.append(0)
            
            return features
            
        except Exception as e:
            logger.error(f"خطأ في استخراج ميزات المؤشرات: {e}")
            return []
    
    def _extract_technical_analysis_features(self, technical_analysis: Dict) -> List[float]:
        """استخراج ميزات من التحليل الفني"""
        try:
            features = []
            
            # قوة الإشارة الفنية
            confidence = technical_analysis.get('confidence', 0) / 100
            features.append(confidence)
            
            # اتجاه الإشارة
            direction = technical_analysis.get('direction', 'HOLD')
            if direction == 'BULLISH':
                features.append(1)
            elif direction == 'BEARISH':
                features.append(-1)
            else:
                features.append(0)
            
            # عدد المؤشرات الداعمة
            supporting_indicators = technical_analysis.get('supporting_indicators', 0)
            features.append(supporting_indicators / 10)  # تطبيع
            
            # نتائج الإشارات الفردية
            signals_breakdown = technical_analysis.get('signals_breakdown', {})
            bullish_signals = len(signals_breakdown.get('bullish', []))
            bearish_signals = len(signals_breakdown.get('bearish', []))
            
            features.append(bullish_signals / 10)
            features.append(bearish_signals / 10)
            features.append((bullish_signals - bearish_signals) / 10)
            
            # جودة الإشارة
            quality_score = technical_analysis.get('quality_score', 0.5)
            features.append(quality_score)
            
            return features
            
        except Exception as e:
            logger.error(f"خطأ في استخراج ميزات التحليل الفني: {e}")
            return []
    
    def _extract_quantitative_analysis_features(self, quantitative_analysis: Dict) -> List[float]:
        """استخراج ميزات من التحليل الكمي"""
        try:
            features = []
            
            # النتيجة الكمية الإجمالية
            overall_score = quantitative_analysis.get('overall_score', 0)
            features.append(overall_score)
            
            # قوة الإشارة الكمية
            signal_strength = quantitative_analysis.get('signal_strength', 'unknown')
            strength_map = {'very_weak': 0.1, 'weak': 0.3, 'moderate': 0.5, 'strong': 0.7, 'very_strong': 0.9}
            features.append(strength_map.get(signal_strength, 0.5))
            
            # جودة البيانات
            data_quality = quantitative_analysis.get('data_quality', {}).get('overall_score', 0.5)
            features.append(data_quality)
            
            # تحليل Z-Score
            detailed_analyses = quantitative_analysis.get('detailed_analyses', {})
            zscore_analysis = detailed_analyses.get('zscore', {})
            if zscore_analysis:
                overall_assessment = zscore_analysis.get('overall_assessment', {})
                features.append(overall_assessment.get('confidence', 0))
                features.append(overall_assessment.get('extreme_deviations_count', 0) / 5)
                features.append(min(overall_assessment.get('max_absolute_zscore', 0) / 3, 1))
            else:
                features.extend([0, 0, 0])
            
            # تحليل التقلبات
            volatility_analysis = detailed_analyses.get('volatility', {})
            if volatility_analysis:
                current_vol = volatility_analysis.get('current_volatility', {})
                vol_level_map = {'very_low': 0.1, 'low': 0.3, 'normal': 0.5, 'high': 0.7, 'very_high': 0.9}
                vol_level = current_vol.get('level', 'normal')
                features.append(vol_level_map.get(vol_level, 0.5))
            else:
                features.append(0.5)
            
            return features
            
        except Exception as e:
            logger.error(f"خطأ في استخراج ميزات التحليل الكمي: {e}")
            return []

    def _extract_behavioral_analysis_features(self, behavioral_analysis: Dict) -> List[float]:
        """استخراج ميزات من التحليل السلوكي"""
        try:
            features = []

            # النتيجة السلوكية
            behavioral_score = behavioral_analysis.get('behavioral_score', 0)
            features.append(behavioral_score)

            # المعنويات الغالبة
            dominant_sentiment = behavioral_analysis.get('dominant_sentiment', 'NEUTRAL')
            sentiment_map = {'EXTREME_FEAR': -1, 'FEAR': -0.5, 'NEUTRAL': 0, 'GREED': 0.5, 'EXTREME_GREED': 1}
            features.append(sentiment_map.get(dominant_sentiment, 0))

            # قوة الإشارة السلوكية
            signal_strength = behavioral_analysis.get('signal_strength', 0.5)
            features.append(signal_strength)

            # الاتساق السلوكي
            behavioral_consistency = behavioral_analysis.get('behavioral_consistency', 0.5)
            features.append(behavioral_consistency)

            # تحليل تفصيلي
            detailed_analyses = behavioral_analysis.get('detailed_analyses', {})

            # أنماط الشموع
            candle_patterns = detailed_analyses.get('candle_patterns', {})
            if candle_patterns:
                pattern_reliability = candle_patterns.get('pattern_reliability', {}).get('score', 0.5)
                features.append(pattern_reliability)
                patterns_count = len(candle_patterns.get('patterns_detected', []))
                features.append(min(patterns_count / 5, 1))  # تطبيع عدد الأنماط
            else:
                features.extend([0.5, 0])

            # تحليل الخوف والطمع
            fear_greed = detailed_analyses.get('fear_greed', {})
            if fear_greed:
                emotional_intensity = fear_greed.get('emotional_intensity', 0.5)
                features.append(emotional_intensity)

                fear_greed_index = fear_greed.get('fear_greed_index', 0)
                features.append(np.tanh(fear_greed_index))  # تطبيع
            else:
                features.extend([0.5, 0])

            return features

        except Exception as e:
            logger.error(f"خطأ في استخراج ميزات التحليل السلوكي: {e}")
            return []

    def _extract_derived_features(self, candles: List[Dict], indicators: Dict) -> List[float]:
        """استخراج ميزات مشتقة"""
        try:
            features = []

            if len(candles) < 5:
                return []

            # ميزات الاتجاه
            recent_closes = [c['close'] for c in candles[-5:]]
            trend_slope = np.polyfit(range(len(recent_closes)), recent_closes, 1)[0]
            features.append(trend_slope / recent_closes[-1])  # تطبيع الميل

            # قوة الاتجاه
            price_changes = np.diff(recent_closes)
            trend_strength = np.sum(np.sign(price_changes[:-1]) == np.sign(price_changes[1:])) / (len(price_changes) - 1)
            features.append(trend_strength)

            # تسارع السعر
            if len(price_changes) >= 2:
                acceleration = np.mean(np.diff(price_changes))
                features.append(np.tanh(acceleration / recent_closes[-1]))
            else:
                features.append(0)

            # نسبة المخاطر/العوائد
            if len(recent_closes) >= 3:
                returns = np.diff(recent_closes) / recent_closes[:-1]
                if np.std(returns) > 0:
                    sharpe_like = np.mean(returns) / np.std(returns)
                    features.append(np.tanh(sharpe_like))
                else:
                    features.append(0)
            else:
                features.append(0)

            # تفاعل المؤشرات
            rsi_5 = indicators.get('rsi_5', 50)
            rsi_14 = indicators.get('rsi_14', 50)
            features.append((rsi_5 - rsi_14) / 100)  # فرق RSI

            # تفاعل EMA
            ema_5 = indicators.get('ema_5', recent_closes[-1])
            ema_10 = indicators.get('ema_10', recent_closes[-1])
            if ema_10 > 0:
                features.append((ema_5 - ema_10) / ema_10)
            else:
                features.append(0)

            return features

        except Exception as e:
            logger.error(f"خطأ في استخراج الميزات المشتقة: {e}")
            return []

    def _clean_features(self, features: List[float]) -> List[float]:
        """تنظيف الميزات"""
        try:
            cleaned = []
            for feature in features:
                if np.isnan(feature) or np.isinf(feature):
                    cleaned.append(0.0)
                else:
                    # قطع القيم الشاذة
                    cleaned.append(max(-10, min(10, float(feature))))

            return cleaned

        except Exception:
            return [0.0] * len(features)

    def _make_predictions(self, features: List[float]) -> Dict[str, Any]:
        """التنبؤ باستخدام النماذج"""
        try:
            predictions = {}

            # تحضير البيانات
            X = np.array(features).reshape(1, -1)

            # تطبيق التطبيع
            X_scaled = self.scaler.transform(X)

            # تطبيق اختيار الميزات
            X_selected = self.feature_selector.transform(X_scaled)

            # التنبؤ بكل نموذج
            for model_name, model in self.models.items():
                try:
                    # التنبؤ
                    prediction = model.predict(X_selected)[0]
                    probabilities = model.predict_proba(X_selected)[0]

                    # تحويل التنبؤ إلى نص
                    prediction_text = self.label_encoder.inverse_transform([prediction])[0]

                    # توزيع الاحتماليات
                    prob_dict = {}
                    for i, prob in enumerate(probabilities):
                        class_name = self.label_encoder.inverse_transform([i])[0]
                        prob_dict[class_name] = float(prob)

                    predictions[model_name] = {
                        'prediction': prediction_text,
                        'confidence': float(max(probabilities)),
                        'probabilities': prob_dict
                    }

                except Exception as e:
                    logger.error(f"خطأ في التنبؤ بالنموذج {model_name}: {e}")
                    predictions[model_name] = {
                        'prediction': 'HOLD',
                        'confidence': 0.0,
                        'probabilities': {'HOLD': 1.0}
                    }

            return predictions

        except Exception as e:
            logger.error(f"خطأ في التنبؤ: {e}")
            return {}

    def _ensemble_predictions(self, predictions: Dict[str, Any]) -> Dict[str, Any]:
        """دمج تنبؤات النماذج"""
        try:
            if not predictions:
                return {'prediction': 'HOLD', 'confidence': 0.0, 'probabilities': {'HOLD': 1.0}}

            # جمع التنبؤات
            all_predictions = []
            all_confidences = []
            combined_probabilities = {}

            for model_name, pred_data in predictions.items():
                prediction = pred_data['prediction']
                confidence = pred_data['confidence']
                probabilities = pred_data['probabilities']

                all_predictions.append(prediction)
                all_confidences.append(confidence)

                # دمج الاحتماليات
                for class_name, prob in probabilities.items():
                    if class_name not in combined_probabilities:
                        combined_probabilities[class_name] = []
                    combined_probabilities[class_name].append(prob)

            # حساب متوسط الاحتماليات
            avg_probabilities = {}
            for class_name, probs in combined_probabilities.items():
                avg_probabilities[class_name] = np.mean(probs)

            # تحديد التنبؤ النهائي
            final_prediction = max(avg_probabilities, key=avg_probabilities.get)
            final_confidence = avg_probabilities[final_prediction]

            # تطبيق عتبة الثقة
            if final_confidence < 0.6:
                final_prediction = 'HOLD'
                final_confidence = 1 - final_confidence

            return {
                'prediction': final_prediction,
                'confidence': final_confidence,
                'probabilities': avg_probabilities
            }

        except Exception as e:
            logger.error(f"خطأ في دمج التنبؤات: {e}")
            return {'prediction': 'HOLD', 'confidence': 0.0, 'probabilities': {'HOLD': 1.0}}

    def train_models(self, training_data: List[Dict]) -> bool:
        """تدريب النماذج"""
        try:
            if len(training_data) < self.training_config['min_samples']:
                logger.warning(f"عدد العينات غير كافي للتدريب: {len(training_data)}")
                return False

            # تحضير البيانات
            X, y = self._prepare_training_data(training_data)

            if len(X) == 0:
                logger.error("فشل في تحضير بيانات التدريب")
                return False

            # تقسيم البيانات
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=self.training_config['test_size'],
                random_state=42, stratify=y
            )

            # تطبيع البيانات
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)

            # اختيار الميزات
            X_train_selected = self.feature_selector.fit_transform(X_train_scaled, y_train)
            X_test_selected = self.feature_selector.transform(X_test_scaled)

            # تدريب النماذج
            for model_name, model in self.models.items():
                logger.info(f"تدريب النموذج: {model_name}")

                # التدريب
                model.fit(X_train_selected, y_train)

                # التقييم
                y_pred = model.predict(X_test_selected)

                # حساب المقاييس
                accuracy = accuracy_score(y_test, y_pred)
                precision = precision_score(y_test, y_pred, average='weighted', zero_division=0)
                recall = recall_score(y_test, y_pred, average='weighted', zero_division=0)
                f1 = f1_score(y_test, y_pred, average='weighted', zero_division=0)

                # التحقق المتقاطع
                cv_scores = cross_val_score(model, X_train_selected, y_train,
                                          cv=self.training_config['cv_folds'])

                self.model_performance[model_name] = {
                    'accuracy': accuracy,
                    'precision': precision,
                    'recall': recall,
                    'f1_score': f1,
                    'cv_mean': np.mean(cv_scores),
                    'cv_std': np.std(cv_scores)
                }

                logger.info(f"أداء النموذج {model_name}: دقة={accuracy:.3f}, F1={f1:.3f}")

            # حفظ النماذج
            self._save_models()
            self.models_trained = True

            return True

        except Exception as e:
            logger.error(f"خطأ في تدريب النماذج: {e}")
            return False

    def _save_models(self):
        """حفظ النماذج المدربة"""
        try:
            # حفظ النماذج
            for model_name, model in self.models.items():
                model_path = self.models_dir / f"{model_name}.joblib"
                joblib.dump(model, model_path)

            # حفظ المعالجات
            joblib.dump(self.scaler, self.models_dir / "scaler.joblib")
            joblib.dump(self.label_encoder, self.models_dir / "label_encoder.joblib")
            joblib.dump(self.feature_selector, self.models_dir / "feature_selector.joblib")

            # حفظ أداء النماذج
            with open(self.models_dir / "model_performance.json", 'w') as f:
                json.dump(self.model_performance, f, indent=2)

            logger.info("تم حفظ النماذج بنجاح")

        except Exception as e:
            logger.error(f"خطأ في حفظ النماذج: {e}")

    def _load_models(self) -> bool:
        """تحميل النماذج المحفوظة"""
        try:
            # التحقق من وجود الملفات
            required_files = ['scaler.joblib', 'label_encoder.joblib', 'feature_selector.joblib']
            for filename in required_files:
                if not (self.models_dir / filename).exists():
                    return False

            # تحميل المعالجات
            self.scaler = joblib.load(self.models_dir / "scaler.joblib")
            self.label_encoder = joblib.load(self.models_dir / "label_encoder.joblib")
            self.feature_selector = joblib.load(self.models_dir / "feature_selector.joblib")

            # تحميل النماذج
            for model_name in self.models.keys():
                model_path = self.models_dir / f"{model_name}.joblib"
                if model_path.exists():
                    self.models[model_name] = joblib.load(model_path)
                else:
                    return False

            # تحميل أداء النماذج
            performance_path = self.models_dir / "model_performance.json"
            if performance_path.exists():
                with open(performance_path, 'r') as f:
                    self.model_performance = json.load(f)

            self.models_trained = True
            logger.info("تم تحميل النماذج بنجاح")
            return True

        except Exception as e:
            logger.error(f"خطأ في تحميل النماذج: {e}")
            return False

    def _prepare_training_data(self, training_data: List[Dict]) -> Tuple[np.ndarray, np.ndarray]:
        """تحضير بيانات التدريب"""
        try:
            X = []
            y = []

            for data_point in training_data:
                # استخراج الميزات
                features = self._extract_features(
                    data_point.get('candles', []),
                    data_point.get('indicators', {}),
                    data_point.get('technical_analysis'),
                    data_point.get('quantitative_analysis'),
                    data_point.get('behavioral_analysis')
                )

                if features:
                    X.append(features)
                    # النتيجة المتوقعة (CALL, PUT, HOLD)
                    result = data_point.get('result', 'HOLD')
                    y.append(result)

            if not X:
                return np.array([]), np.array([])

            # تحويل إلى numpy arrays
            X = np.array(X)
            y = np.array(y)

            # ترميز التصنيفات
            y_encoded = self.label_encoder.fit_transform(y)

            return X, y_encoded

        except Exception as e:
            logger.error(f"خطأ في تحضير بيانات التدريب: {e}")
            return np.array([]), np.array([])

    def _analyze_feature_importance(self, features: List[float]) -> Dict[str, float]:
        """تحليل أهمية الميزات"""
        try:
            if not self.models_trained or 'random_forest' not in self.models:
                return {}

            # الحصول على أهمية الميزات من Random Forest
            rf_model = self.models['random_forest']

            if hasattr(rf_model, 'feature_importances_'):
                importances = rf_model.feature_importances_

                # إنشاء أسماء الميزات
                feature_names = self._generate_feature_names()

                # دمج الأسماء مع الأهمية
                feature_importance = {}
                for i, importance in enumerate(importances):
                    if i < len(feature_names):
                        feature_importance[feature_names[i]] = float(importance)

                # ترتيب حسب الأهمية
                sorted_features = dict(sorted(feature_importance.items(),
                                            key=lambda x: x[1], reverse=True))

                return sorted_features

            return {}

        except Exception as e:
            logger.error(f"خطأ في تحليل أهمية الميزات: {e}")
            return {}

    def _generate_feature_names(self) -> List[str]:
        """توليد أسماء الميزات"""
        names = []

        # ميزات الشموع
        names.extend(['price_mean', 'price_std', 'price_change_rel'])
        names.extend(['volume_mean', 'volume_std', 'volume_ratio'])

        # ميزات الشموع الفردية (آخر 3 شموع)
        for i in range(3):
            names.extend([f'candle_{i}_body_ratio', f'candle_{i}_direction',
                         f'candle_{i}_upper_wick', f'candle_{i}_lower_wick'])

        # ميزات المؤشرات
        names.extend(['rsi_5_norm', 'rsi_5_zone', 'rsi_14_norm', 'rsi_14_zone'])
        names.extend(['ema_5', 'ema_10', 'ema_21'])
        names.extend(['ema_5_vs_10', 'ema_10_vs_21'])
        names.extend(['macd_line', 'macd_signal', 'macd_cross', 'macd_histogram'])
        names.extend(['bb_width', 'bb_position'])
        names.extend(['momentum_10_norm', 'zscore_20_norm'])

        # ميزات التحليل الفني
        names.extend(['ta_confidence', 'ta_direction', 'ta_supporting_indicators',
                     'ta_bullish_signals', 'ta_bearish_signals', 'ta_signal_balance', 'ta_quality'])

        # ميزات التحليل الكمي
        names.extend(['qa_overall_score', 'qa_signal_strength', 'qa_data_quality',
                     'qa_zscore_confidence', 'qa_zscore_deviations', 'qa_zscore_max',
                     'qa_volatility_level'])

        # ميزات التحليل السلوكي
        names.extend(['ba_behavioral_score', 'ba_dominant_sentiment', 'ba_signal_strength',
                     'ba_consistency', 'ba_pattern_reliability', 'ba_patterns_count',
                     'ba_emotional_intensity', 'ba_fear_greed_index'])

        # ميزات مشتقة
        names.extend(['trend_slope', 'trend_strength', 'price_acceleration',
                     'risk_reward_ratio', 'rsi_diff', 'ema_diff'])

        return names

    def _assess_prediction_quality(self, predictions: Dict[str, Any], features: List[float]) -> Dict[str, Any]:
        """تقييم جودة التنبؤ"""
        try:
            quality_metrics = {}

            # تنوع التنبؤات
            unique_predictions = set(pred['prediction'] for pred in predictions.values())
            diversity_score = len(unique_predictions) / len(predictions) if predictions else 0
            quality_metrics['prediction_diversity'] = diversity_score

            # متوسط الثقة
            confidences = [pred['confidence'] for pred in predictions.values()]
            avg_confidence = np.mean(confidences) if confidences else 0
            quality_metrics['average_confidence'] = avg_confidence

            # استقرار التنبؤات
            prediction_counts = {}
            for pred in predictions.values():
                prediction = pred['prediction']
                prediction_counts[prediction] = prediction_counts.get(prediction, 0) + 1

            max_count = max(prediction_counts.values()) if prediction_counts else 0
            stability_score = max_count / len(predictions) if predictions else 0
            quality_metrics['prediction_stability'] = stability_score

            # جودة الميزات
            feature_quality = self._assess_feature_quality(features)
            quality_metrics['feature_quality'] = feature_quality

            # النتيجة الإجمالية
            overall_quality = np.mean([
                avg_confidence,
                stability_score,
                feature_quality,
                min(diversity_score * 2, 1.0)  # تفضيل التنوع المعتدل
            ])

            quality_metrics['overall_quality'] = overall_quality

            return quality_metrics

        except Exception as e:
            logger.error(f"خطأ في تقييم جودة التنبؤ: {e}")
            return {'overall_quality': 0.5}

    def _assess_feature_quality(self, features: List[float]) -> float:
        """تقييم جودة الميزات"""
        try:
            if not features:
                return 0.0

            # التحقق من القيم الشاذة
            features_array = np.array(features)
            outlier_ratio = np.sum(np.abs(features_array) > 5) / len(features_array)

            # التحقق من التنوع
            unique_ratio = len(np.unique(features_array)) / len(features_array)

            # التحقق من القيم المفقودة
            missing_ratio = np.sum(features_array == 0) / len(features_array)

            # حساب النتيجة
            quality_score = (
                (1 - outlier_ratio) * 0.3 +
                unique_ratio * 0.4 +
                (1 - missing_ratio) * 0.3
            )

            return max(0, min(1, quality_score))

        except Exception:
            return 0.5

    def _get_insufficient_data_result(self) -> Dict[str, Any]:
        """نتيجة عدم كفاية البيانات"""
        return {
            'signal_type': 'AI_ANALYSIS',
            'prediction': 'HOLD',
            'confidence': 0,
            'reason': 'insufficient_data_for_ai_analysis',
            'timestamp': pd.Timestamp.now().isoformat()
        }

    def _get_feature_extraction_error(self) -> Dict[str, Any]:
        """نتيجة خطأ استخراج الميزات"""
        return {
            'signal_type': 'AI_ANALYSIS',
            'prediction': 'HOLD',
            'confidence': 0,
            'reason': 'feature_extraction_error',
            'timestamp': pd.Timestamp.now().isoformat()
        }

    def _get_untrained_models_result(self) -> Dict[str, Any]:
        """نتيجة النماذج غير المدربة"""
        return {
            'signal_type': 'AI_ANALYSIS',
            'prediction': 'HOLD',
            'confidence': 0,
            'reason': 'models_not_trained',
            'timestamp': pd.Timestamp.now().isoformat()
        }

    def _get_error_result(self) -> Dict[str, Any]:
        """نتيجة الخطأ"""
        return {
            'signal_type': 'AI_ANALYSIS',
            'prediction': 'HOLD',
            'confidence': 0,
            'reason': 'ai_analysis_error',
            'timestamp': pd.Timestamp.now().isoformat()
        }
