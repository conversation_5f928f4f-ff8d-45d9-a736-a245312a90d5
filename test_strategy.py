#!/usr/bin/env python3
"""
🧪 اختبارات استراتيجية السكالبينغ الاحترافية
Test Suite for Professional Scalping Strategy

يتضمن اختبارات شاملة لجميع مكونات الاستراتيجية
"""

import unittest
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import json
import os
import sys

# إضافة المجلد الحالي إلى المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد المكونات للاختبار
try:
    from technical_analysis_engine import TechnicalAnalysisEngine, TrendDirection, SignalStrength
    from quantitative_analysis_engine import QuantitativeAnalysisEngine
    from behavioral_analysis_engine import BehavioralAnalysisEngine, MarketSentiment
    from ai_analysis_engine import AIAnalysisEngine
    from professional_scalping_strategy import ProfessionalScalpingStrategy, SignalDecision
    print("✅ تم استيراد جميع المكونات بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد المكونات: {e}")
    sys.exit(1)

class TestDataGenerator:
    """مولد بيانات الاختبار"""
    
    @staticmethod
    def generate_sample_candles(count: int = 100, trend: str = 'sideways') -> list:
        """توليد شموع عينة للاختبار"""
        candles = []
        base_price = 1.1000
        
        for i in range(count):
            # تحديد اتجاه السعر
            if trend == 'uptrend':
                price_change = np.random.normal(0.0001, 0.0005)
            elif trend == 'downtrend':
                price_change = np.random.normal(-0.0001, 0.0005)
            else:  # sideways
                price_change = np.random.normal(0, 0.0003)
            
            # حساب الأسعار
            open_price = base_price + price_change
            close_price = open_price + np.random.normal(0, 0.0002)
            high_price = max(open_price, close_price) + abs(np.random.normal(0, 0.0001))
            low_price = min(open_price, close_price) - abs(np.random.normal(0, 0.0001))
            
            candle = {
                'open': round(open_price, 5),
                'high': round(high_price, 5),
                'low': round(low_price, 5),
                'close': round(close_price, 5),
                'volume': np.random.randint(1000, 10000),
                'timestamp': datetime.now() - timedelta(minutes=count-i)
            }
            
            candles.append(candle)
            base_price = close_price
        
        return candles
    
    @staticmethod
    def generate_sample_indicators(candles: list) -> dict:
        """توليد مؤشرات عينة"""
        closes = [c['close'] for c in candles]
        
        # حساب مؤشرات بسيطة
        indicators = {
            'rsi_5': min(max(np.random.normal(50, 15), 0), 100),
            'rsi_14': min(max(np.random.normal(50, 20), 0), 100),
            'ema_5': np.mean(closes[-5:]) if len(closes) >= 5 else closes[-1],
            'ema_10': np.mean(closes[-10:]) if len(closes) >= 10 else closes[-1],
            'ema_21': np.mean(closes[-21:]) if len(closes) >= 21 else closes[-1],
            'momentum_10': closes[-1] - closes[-10] if len(closes) >= 10 else 0,
            'zscore_20': np.random.normal(0, 1),
            'macd': {
                'macd': np.random.normal(0, 0.0001),
                'signal': np.random.normal(0, 0.0001),
                'histogram': np.random.normal(0, 0.0001)
            },
            'bollinger_bands': {
                'upper': closes[-1] + 0.002,
                'middle': closes[-1],
                'lower': closes[-1] - 0.002
            }
        }
        
        return indicators

class TestTechnicalAnalysisEngine(unittest.TestCase):
    """اختبارات محرك التحليل الفني"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.engine = TechnicalAnalysisEngine()
        self.sample_candles = TestDataGenerator.generate_sample_candles(50)
        self.sample_indicators = TestDataGenerator.generate_sample_indicators(self.sample_candles)
    
    def test_engine_initialization(self):
        """اختبار تهيئة المحرك"""
        self.assertIsInstance(self.engine, TechnicalAnalysisEngine)
        self.assertIn('ema_crossover', self.engine.indicator_weights)
        self.assertIn('rsi_divergence', self.engine.indicator_weights)
    
    def test_analyze_market_with_sufficient_data(self):
        """اختبار التحليل مع بيانات كافية"""
        result = self.engine.analyze_market(self.sample_candles, self.sample_indicators)
        
        self.assertIsInstance(result, dict)
        self.assertIn('signal_type', result)
        self.assertEqual(result['signal_type'], 'TECHNICAL_ANALYSIS')
        self.assertIn('direction', result)
        self.assertIn('confidence', result)
    
    def test_analyze_market_with_insufficient_data(self):
        """اختبار التحليل مع بيانات غير كافية"""
        insufficient_candles = TestDataGenerator.generate_sample_candles(10)
        result = self.engine.analyze_market(insufficient_candles, self.sample_indicators)
        
        self.assertIn('reason', result)
        self.assertEqual(result['reason'], 'insufficient_data')
    
    def test_ema_crossover_detection(self):
        """اختبار كشف تقاطع المتوسطات المتحركة"""
        # إنشاء بيانات تقاطع صاعد
        indicators_bullish = self.sample_indicators.copy()
        indicators_bullish['ema_5'] = 1.1020
        indicators_bullish['ema_10'] = 1.1015
        
        df = self.engine._prepare_dataframe(self.sample_candles, indicators_bullish)
        signal = self.engine._analyze_ema_crossover(df)
        
        # يجب أن تكون النتيجة إما None أو إشارة صحيحة
        if signal is not None:
            self.assertIn(signal.direction, [TrendDirection.BULLISH, TrendDirection.BEARISH])
            self.assertIsInstance(signal.strength, SignalStrength)
    
    def test_rsi_analysis(self):
        """اختبار تحليل RSI"""
        # اختبار RSI في منطقة التشبع البيعي
        indicators_oversold = self.sample_indicators.copy()
        indicators_oversold['rsi_5'] = 25
        
        df = self.engine._prepare_dataframe(self.sample_candles, indicators_oversold)
        signal = self.engine._analyze_rsi_divergence(df)
        
        if signal is not None:
            self.assertEqual(signal.direction, TrendDirection.BULLISH)
            self.assertGreater(signal.confidence, 0)

class TestQuantitativeAnalysisEngine(unittest.TestCase):
    """اختبارات محرك التحليل الكمي"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.engine = QuantitativeAnalysisEngine()
        self.sample_candles = TestDataGenerator.generate_sample_candles(50)
        self.sample_indicators = TestDataGenerator.generate_sample_indicators(self.sample_candles)
    
    def test_engine_initialization(self):
        """اختبار تهيئة المحرك"""
        self.assertIsInstance(self.engine, QuantitativeAnalysisEngine)
        self.assertIn('extreme', self.engine.z_score_thresholds)
        self.assertIn('min_sample_size', self.engine.probability_filters)
    
    def test_zscore_calculation(self):
        """اختبار حساب Z-Score"""
        prices = pd.Series([1.1000, 1.1010, 1.1005, 1.1015, 1.1020])
        zscore = self.engine._calculate_zscore(prices, window=3)
        
        self.assertIsInstance(zscore, pd.Series)
        self.assertEqual(len(zscore), len(prices))
    
    def test_zscore_interpretation(self):
        """اختبار تفسير Z-Score"""
        # اختبار قيم مختلفة
        self.assertEqual(self.engine._interpret_zscore(3.0), 'extreme_deviation')
        self.assertEqual(self.engine._interpret_zscore(2.2), 'strong_deviation')
        self.assertEqual(self.engine._interpret_zscore(1.7), 'moderate_deviation')
        self.assertEqual(self.engine._interpret_zscore(0.5), 'normal_range')
    
    def test_analyze_market(self):
        """اختبار التحليل الكمي الشامل"""
        result = self.engine.analyze_market(self.sample_candles, self.sample_indicators)
        
        self.assertIsInstance(result, dict)
        self.assertIn('signal_type', result)
        self.assertEqual(result['signal_type'], 'QUANTITATIVE_ANALYSIS')

class TestBehavioralAnalysisEngine(unittest.TestCase):
    """اختبارات محرك التحليل السلوكي"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.engine = BehavioralAnalysisEngine()
        self.sample_candles = TestDataGenerator.generate_sample_candles(50)
        self.sample_indicators = TestDataGenerator.generate_sample_indicators(self.sample_candles)
    
    def test_engine_initialization(self):
        """اختبار تهيئة المحرك"""
        self.assertIsInstance(self.engine, BehavioralAnalysisEngine)
        self.assertIn('doji_body_ratio', self.engine.candle_thresholds)
        self.assertIn('volume_spike_threshold', self.engine.sentiment_factors)
    
    def test_doji_detection(self):
        """اختبار كشف نمط Doji"""
        # إنشاء شمعة Doji
        doji_candle = pd.Series({
            'open': 1.1000,
            'close': 1.1001,  # جسم صغير جداً
            'high': 1.1010,
            'low': 1.0990,
            'body_ratio': 0.05  # نسبة جسم صغيرة
        })
        
        is_doji = self.engine._is_doji(doji_candle)
        self.assertTrue(is_doji)
    
    def test_hammer_detection(self):
        """اختبار كشف نمط Hammer"""
        # إنشاء شمعة Hammer
        hammer_candle = pd.Series({
            'open': 1.1000,
            'close': 1.1005,
            'high': 1.1007,
            'low': 1.0980,  # ذيل سفلي طويل
            'body_ratio': 0.2,
            'lower_wick_ratio': 0.7,
            'upper_wick_ratio': 0.1
        })
        
        is_hammer = self.engine._is_hammer(hammer_candle)
        self.assertTrue(is_hammer)
    
    def test_sentiment_analysis(self):
        """اختبار تحليل المعنويات"""
        result = self.engine.analyze_market(self.sample_candles, self.sample_indicators)
        
        self.assertIsInstance(result, dict)
        self.assertIn('signal_type', result)
        self.assertEqual(result['signal_type'], 'BEHAVIORAL_ANALYSIS')

class TestAIAnalysisEngine(unittest.TestCase):
    """اختبارات محرك الذكاء الاصطناعي"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.engine = AIAnalysisEngine()
        self.sample_candles = TestDataGenerator.generate_sample_candles(50)
        self.sample_indicators = TestDataGenerator.generate_sample_indicators(self.sample_candles)
    
    def test_engine_initialization(self):
        """اختبار تهيئة المحرك"""
        self.assertIsInstance(self.engine, AIAnalysisEngine)
        self.assertIn('random_forest', self.engine.models)
        self.assertIn('gradient_boosting', self.engine.models)
    
    def test_feature_extraction(self):
        """اختبار استخراج الميزات"""
        features = self.engine._extract_features(
            self.sample_candles, 
            self.sample_indicators,
            None, None, None
        )
        
        self.assertIsInstance(features, list)
        self.assertGreater(len(features), 0)
        
        # التحقق من أن جميع الميزات أرقام
        for feature in features:
            self.assertIsInstance(feature, (int, float))
            self.assertFalse(np.isnan(feature))
            self.assertFalse(np.isinf(feature))
    
    def test_feature_cleaning(self):
        """اختبار تنظيف الميزات"""
        dirty_features = [1.0, np.nan, np.inf, -np.inf, 2.5]
        clean_features = self.engine._clean_features(dirty_features)
        
        self.assertEqual(len(clean_features), len(dirty_features))
        for feature in clean_features:
            self.assertFalse(np.isnan(feature))
            self.assertFalse(np.isinf(feature))

class TestProfessionalScalpingStrategy(unittest.TestCase):
    """اختبارات الاستراتيجية الموحدة"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.strategy = ProfessionalScalpingStrategy()
        self.sample_candles = TestDataGenerator.generate_sample_candles(50)
        self.sample_indicators = TestDataGenerator.generate_sample_indicators(self.sample_candles)
    
    def test_strategy_initialization(self):
        """اختبار تهيئة الاستراتيجية"""
        self.assertIsInstance(self.strategy, ProfessionalScalpingStrategy)
        self.assertIn('technical', self.strategy.layer_weights)
        self.assertIn('strong_signal', self.strategy.decision_thresholds)
    
    def test_layer_scores_calculation(self):
        """اختبار حساب نتائج الطبقات"""
        # إنشاء نتائج وهمية للطبقات
        technical = {'direction': 'BULLISH', 'confidence': 80, 'quality_score': 0.7}
        quantitative = {'overall_score': 0.6, 'confidence': 0.7}
        behavioral = {'behavioral_score': 0.5, 'confidence': 0.6}
        ai = {'prediction': 'CALL', 'confidence': 0.8, 'prediction_quality': {'overall_quality': 0.7}}
        
        scores = self.strategy._calculate_layer_scores(technical, quantitative, behavioral, ai)
        
        self.assertIsInstance(scores, dict)
        self.assertIn('technical', scores)
        self.assertIn('quantitative', scores)
        self.assertIn('behavioral', scores)
        self.assertIn('ai', scores)
        
        # التحقق من أن النتائج في النطاق الصحيح
        for score in scores.values():
            self.assertGreaterEqual(score, 0)
            self.assertLessEqual(score, 1)
    
    def test_overall_score_calculation(self):
        """اختبار حساب النتيجة الإجمالية"""
        layer_scores = {
            'technical': 0.8,
            'quantitative': 0.6,
            'behavioral': 0.7,
            'ai': 0.9
        }
        
        overall_score = self.strategy._calculate_overall_score(layer_scores)
        
        self.assertIsInstance(overall_score, float)
        self.assertGreaterEqual(overall_score, 0)
        self.assertLessEqual(overall_score, 1)
    
    def test_signal_strength_determination(self):
        """اختبار تحديد قوة الإشارة"""
        self.assertEqual(self.strategy._determine_signal_strength(0.9), 'very_strong')
        self.assertEqual(self.strategy._determine_signal_strength(0.8), 'strong')
        self.assertEqual(self.strategy._determine_signal_strength(0.7), 'moderate')
        self.assertEqual(self.strategy._determine_signal_strength(0.6), 'weak')
    
    def test_final_decision_making(self):
        """اختبار اتخاذ القرار النهائي"""
        # اختبار قرار صاعد قوي
        decision = self.strategy._make_final_decision('BULLISH', 'very_strong')
        self.assertEqual(decision, SignalDecision.STRONG_CALL)
        
        # اختبار قرار هابط متوسط
        decision = self.strategy._make_final_decision('BEARISH', 'moderate')
        self.assertEqual(decision, SignalDecision.WEAK_PUT)
        
        # اختبار عدم وجود إشارة
        decision = self.strategy._make_final_decision('HOLD', 'weak')
        self.assertEqual(decision, SignalDecision.HOLD)

class TestPerformanceMetrics(unittest.TestCase):
    """اختبارات مقاييس الأداء"""
    
    def test_win_rate_calculation(self):
        """اختبار حساب معدل النجاح"""
        winning_trades = 75
        total_trades = 100
        win_rate = winning_trades / total_trades
        
        self.assertEqual(win_rate, 0.75)
        self.assertGreaterEqual(win_rate, 0)
        self.assertLessEqual(win_rate, 1)
    
    def test_sharpe_ratio_calculation(self):
        """اختبار حساب نسبة شارب"""
        returns = np.array([0.01, 0.02, -0.005, 0.015, 0.008])
        mean_return = np.mean(returns)
        std_return = np.std(returns)
        
        if std_return > 0:
            sharpe_ratio = mean_return / std_return
            self.assertIsInstance(sharpe_ratio, float)
    
    def test_max_drawdown_calculation(self):
        """اختبار حساب أقصى انخفاض"""
        cumulative_returns = np.array([0.01, 0.03, 0.025, 0.04, 0.035])
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdown = cumulative_returns - running_max
        max_drawdown = np.min(drawdown)
        
        self.assertLessEqual(max_drawdown, 0)

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء تشغيل اختبارات استراتيجية السكالبينغ الاحترافية")
    print("=" * 60)
    
    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestSuite()
    
    # إضافة اختبارات المحركات
    test_suite.addTest(unittest.makeSuite(TestTechnicalAnalysisEngine))
    test_suite.addTest(unittest.makeSuite(TestQuantitativeAnalysisEngine))
    test_suite.addTest(unittest.makeSuite(TestBehavioralAnalysisEngine))
    test_suite.addTest(unittest.makeSuite(TestAIAnalysisEngine))
    test_suite.addTest(unittest.makeSuite(TestProfessionalScalpingStrategy))
    test_suite.addTest(unittest.makeSuite(TestPerformanceMetrics))
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # عرض النتائج
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبارات:")
    print(f"✅ اختبارات نجحت: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ اختبارات فشلت: {len(result.failures)}")
    print(f"🚫 أخطاء: {len(result.errors)}")
    print(f"📈 معدل النجاح: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print("\n❌ الاختبارات الفاشلة:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError: ')[-1].split('\\n')[0]}")
    
    if result.errors:
        print("\n🚫 الأخطاء:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('\\n')[-2]}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_all_tests()
    
    if success:
        print("\n🎉 جميع الاختبارات نجحت! الاستراتيجية جاهزة للاستخدام.")
        sys.exit(0)
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء وإصلاحها.")
        sys.exit(1)
