# متطلبات استراتيجية السكالبينغ الاحترافية - PyQuotex Advanced

# المتطلبات الأساسية الموجودة
beautifulsoup4==4.13.4
certifi==2025.6.15
charset-normalizer==3.4.2
idna==3.10
numpy==2.3.1
pyfiglet==1.0.3
requests==2.32.4
soupsieve==2.7
typing_extensions==4.14.1
urllib3==2.5.0
websocket-client==1.8.0

# مكتبات إضافية للاستراتيجية المتقدمة
pandas>=1.3.0
scipy>=1.7.0
scikit-learn>=1.0.0
joblib>=1.1.0
aiohttp>=3.8.0
websockets>=10.0
cryptography>=3.4.0
matplotlib>=3.5.0
seaborn>=0.11.0
psutil>=5.8.0
statsmodels>=0.13.0
pytz>=2021.3
tqdm>=4.62.0
colorama>=0.4.4
plotly>=5.6.0

# مكتبات اختيارية للتحليل المتقدم
# xgboost>=1.5.0
# lightgbm>=3.3.0
# ta-lib>=0.4.0  # يتطلب تثبيت خاص

# ملاحظات:
# للتثبيت الأساسي: pip install -r requirements.txt
# للمكتبات الاختيارية: قم بإلغاء التعليق وتثبيتها حسب الحاجة
