# 📚 التوثيق الفني - استراتيجية السكالبينغ الاحترافية

## 🏗️ هندسة النظام

### نظرة عامة على البنية
```
┌─────────────────────────────────────────────────────────────┐
│                    واجهة المستخدم                          │
│                 (main_trading_app.py)                      │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 الاستراتيجية الموحدة                       │
│           (professional_scalping_strategy.py)              │
└─┬─────────┬─────────┬─────────┬─────────────────────────────┘
  │         │         │         │
  ▼         ▼         ▼         ▼
┌───────┐ ┌───────┐ ┌───────┐ ┌─────────┐
│التحليل│ │التحليل│ │التحليل│ │ الذكاء  │
│الفني  │ │الكمي  │ │السلوكي│ │الاصطناعي│
└───────┘ └───────┘ └───────┘ └─────────┘
                      │
            ┌─────────▼─────────┐
            │   تكامل Quotex   │
            │ (integration.py)  │
            └───────────────────┘
```

## 🔧 طبقات التحليل

### 1. التحليل الفني (Technical Analysis Engine)

#### المؤشرات المستخدمة:
```python
indicators = {
    'ema_crossover': {
        'periods': [5, 10, 21],
        'weight': 0.25,
        'signals': ['golden_cross', 'death_cross']
    },
    'rsi_divergence': {
        'periods': [5, 14],
        'weight': 0.20,
        'thresholds': {'oversold': 30, 'overbought': 70}
    },
    'bollinger_squeeze': {
        'period': 20,
        'std_dev': 2,
        'weight': 0.15,
        'squeeze_threshold': 0.1
    },
    'macd_momentum': {
        'fast': 12,
        'slow': 26,
        'signal': 9,
        'weight': 0.15
    }
}
```

#### خوارزمية تقاطع المتوسطات:
```python
def _analyze_ema_crossover(self, df):
    ema5_current = df['ema_5'].iloc[-1]
    ema5_prev = df['ema_5'].iloc[-2]
    ema10_current = df['ema_10'].iloc[-1]
    ema10_prev = df['ema_10'].iloc[-2]
    
    # كشف التقاطع الصاعد
    bullish_cross = (ema5_prev <= ema10_prev) and (ema5_current > ema10_current)
    
    if bullish_cross:
        distance = abs(ema5_current - ema10_current) / ema10_current
        strength = self._calculate_signal_strength(distance, [0.001, 0.003, 0.005, 0.01])
        confidence = min(0.8 + distance * 100, 0.95)
        
        return TechnicalSignal(
            indicator='ema_crossover',
            direction=TrendDirection.BULLISH,
            strength=strength,
            confidence=confidence
        )
```

### 2. التحليل الكمي (Quantitative Analysis Engine)

#### Z-Score Analysis:
```python
def _calculate_zscore(self, series, window=20):
    rolling_mean = series.rolling(window=window).mean()
    rolling_std = series.rolling(window=window).std()
    zscore = (series - rolling_mean) / rolling_std
    return zscore

def _interpret_zscore(self, zscore):
    abs_zscore = abs(zscore)
    if abs_zscore >= 2.5:
        return 'extreme_deviation'
    elif abs_zscore >= 2.0:
        return 'strong_deviation'
    elif abs_zscore >= 1.5:
        return 'moderate_deviation'
    else:
        return 'normal_range'
```

#### مصفوفة الارتباط:
```python
def _analyze_correlation_matrix(self, df):
    correlation_matrix = df[analysis_columns].corr()
    
    # كشف الارتباطات القوية
    strong_correlations = []
    for i in range(len(correlation_matrix.columns)):
        for j in range(i+1, len(correlation_matrix.columns)):
            corr_value = correlation_matrix.iloc[i, j]
            if abs(corr_value) > 0.7:
                strong_correlations.append({
                    'pair': (correlation_matrix.columns[i], correlation_matrix.columns[j]),
                    'correlation': corr_value
                })
    
    return strong_correlations
```

### 3. التحليل السلوكي (Behavioral Analysis Engine)

#### كشف أنماط الشموع:
```python
def _is_doji(self, candle):
    body_ratio = abs(candle['close'] - candle['open']) / (candle['high'] - candle['low'])
    return body_ratio <= 0.1

def _is_hammer(self, candle):
    body_size = abs(candle['close'] - candle['open'])
    lower_wick = min(candle['open'], candle['close']) - candle['low']
    upper_wick = candle['high'] - max(candle['open'], candle['close'])
    total_range = candle['high'] - candle['low']
    
    return (lower_wick >= total_range * 0.6 and 
            body_size <= total_range * 0.3 and 
            upper_wick <= total_range * 0.1)
```

#### تحليل الخوف والطمع:
```python
def _detect_fear_indicators(self, df):
    fear_signals = []
    
    for i in range(1, len(df)):
        current = df.iloc[i]
        prev = df.iloc[i-1]
        
        # انخفاض حاد مع حجم عالي
        price_drop = (current['close'] - prev['close']) / prev['close']
        volume_spike = current['volume'] / prev['volume'] if prev['volume'] > 0 else 1
        
        if price_drop < -0.01 and volume_spike > 1.5:
            fear_signals.append({
                'type': 'panic_selling',
                'intensity': abs(price_drop) * volume_spike,
                'timestamp': i
            })
    
    return fear_signals
```

### 4. الذكاء الاصطناعي (AI Analysis Engine)

#### استخراج الميزات:
```python
def _extract_features(self, candles, indicators, technical_analysis, 
                     quantitative_analysis, behavioral_analysis):
    features = []
    
    # ميزات الشموع
    candle_features = self._extract_candle_features(candles)
    features.extend(candle_features)
    
    # ميزات المؤشرات
    indicator_features = self._extract_indicator_features(indicators)
    features.extend(indicator_features)
    
    # ميزات التحليل الفني
    if technical_analysis:
        ta_features = self._extract_technical_analysis_features(technical_analysis)
        features.extend(ta_features)
    
    # ميزات مشتقة
    derived_features = self._extract_derived_features(candles, indicators)
    features.extend(derived_features)
    
    return self._clean_features(features)
```

#### نماذج التعلم الآلي:
```python
models = {
    'random_forest': RandomForestClassifier(
        n_estimators=100,
        max_depth=10,
        random_state=42,
        class_weight='balanced'
    ),
    'gradient_boosting': GradientBoostingClassifier(
        n_estimators=100,
        learning_rate=0.1,
        max_depth=6,
        random_state=42
    )
}
```

## 🔄 تدفق البيانات

### 1. جمع البيانات:
```python
# الحصول على الشموع من Quotex
candles = await client.get_candles(asset, 60, 100)

# حساب المؤشرات الفنية
indicators = self._calculate_indicators(candles)

# تحضير البيانات للتحليل
df = self._prepare_dataframe(candles, indicators)
```

### 2. التحليل المتوازي:
```python
# تشغيل جميع طبقات التحليل
technical_analysis = self.technical_engine.analyze_market(candles, indicators)
quantitative_analysis = self.quantitative_engine.analyze_market(candles, indicators)
behavioral_analysis = self.behavioral_engine.analyze_market(candles, indicators)
ai_analysis = self.ai_engine.analyze_market(candles, indicators, 
                                          technical_analysis, quantitative_analysis, 
                                          behavioral_analysis)
```

### 3. دمج النتائج:
```python
def _synthesize_analyses(self, technical, quantitative, behavioral, ai):
    # حساب النتائج المرجحة
    layer_scores = self._calculate_layer_scores(technical, quantitative, behavioral, ai)
    
    # حساب النتيجة الإجمالية
    overall_score = sum(score * weight for score, weight in 
                       zip(layer_scores.values(), self.layer_weights.values()))
    
    # تحديد الاتجاه الغالب
    dominant_direction = self._determine_dominant_direction(technical, quantitative, 
                                                          behavioral, ai)
    
    # حساب مستوى الثقة
    confidence_level = self._calculate_confidence_level(layer_scores, overall_score)
    
    return self._make_final_decision(dominant_direction, confidence_level)
```

## 📊 خوارزميات اتخاذ القرار

### 1. حساب الأوزان الديناميكية:
```python
def _calculate_layer_scores(self, technical, quantitative, behavioral, ai):
    scores = {}
    
    # نتيجة التحليل الفني
    if technical.get('direction') != 'HOLD':
        ta_confidence = technical.get('confidence', 0) / 100
        ta_quality = technical.get('quality_score', 0.5)
        scores['technical'] = (ta_confidence + ta_quality) / 2
    
    # نتيجة التحليل الكمي
    qa_score = quantitative.get('overall_score', 0)
    qa_confidence = quantitative.get('confidence', 0)
    scores['quantitative'] = (qa_score + qa_confidence) / 2
    
    # نتيجة التحليل السلوكي
    ba_score = behavioral.get('behavioral_score', 0)
    ba_confidence = behavioral.get('confidence', 0)
    scores['behavioral'] = (ba_score + ba_confidence) / 2
    
    # نتيجة الذكاء الاصطناعي
    if ai.get('prediction') != 'HOLD':
        ai_confidence = ai.get('confidence', 0)
        ai_quality = ai.get('prediction_quality', {}).get('overall_quality', 0.5)
        scores['ai'] = (ai_confidence + ai_quality) / 2
    
    return scores
```

### 2. تحديد قوة الإشارة:
```python
def _determine_signal_strength(self, confidence):
    if confidence >= 0.85:
        return 'very_strong'
    elif confidence >= 0.75:
        return 'strong'
    elif confidence >= 0.65:
        return 'moderate'
    else:
        return 'weak'
```

### 3. فلاتر السوق:
```python
def _apply_market_filters(self, signal, candles):
    # فلتر التقلبات
    recent_prices = [c['close'] for c in candles[-5:]]
    volatility = np.std(np.diff(recent_prices) / recent_prices[:-1])
    
    if volatility < 0.001 or volatility > 0.05:
        return False
    
    # فلتر مستوى المخاطر
    if signal.risk_level == 'high' and signal.confidence < 0.8:
        return False
    
    # فلتر وقت التداول
    if not self.strategy.is_trading_time_suitable():
        return False
    
    return True
```

## 🔧 إدارة المخاطر

### 1. حساب حجم الصفقة:
```python
def _calculate_trade_amount(self, signal):
    # المبلغ الأساسي حسب قوة الإشارة
    if signal.decision in [SignalDecision.STRONG_CALL, SignalDecision.STRONG_PUT]:
        base_amount = self.trading_config['default_amount'] * 1.5
    elif signal.decision in [SignalDecision.CALL, SignalDecision.PUT]:
        base_amount = self.trading_config['default_amount']
    else:
        base_amount = self.trading_config['default_amount'] * 0.7
    
    # تعديل حسب مستوى الثقة
    confidence_multiplier = 0.5 + (signal.confidence * 0.5)
    adjusted_amount = base_amount * confidence_multiplier
    
    # تعديل حسب مستوى المخاطر
    if signal.risk_level == 'high':
        adjusted_amount *= 0.7
    elif signal.risk_level == 'low':
        adjusted_amount *= 1.2
    
    # تطبيق حدود المبلغ
    final_amount = max(self.trading_config['min_amount'],
                      min(adjusted_amount, self.trading_config['max_amount']))
    
    # التحقق من نسبة المخاطرة
    max_risk_amount = self.trading_state['balance'] * self.trading_config['risk_percentage']
    final_amount = min(final_amount, max_risk_amount)
    
    return round(final_amount, 2)
```

### 2. مراقبة الأداء:
```python
def _update_performance_metrics(self):
    # حساب العوائد اليومية
    current_balance = self.trading_state['balance']
    daily_return = self.trading_state['daily_profit'] / current_balance
    
    self.performance_metrics['daily_returns'].append({
        'date': datetime.now().date().isoformat(),
        'return': daily_return,
        'profit': self.trading_state['daily_profit']
    })
    
    # حساب Sharpe Ratio
    if len(self.performance_metrics['daily_returns']) > 5:
        returns = [r['return'] for r in self.performance_metrics['daily_returns']]
        if np.std(returns) > 0:
            self.performance_metrics['sharpe_ratio'] = np.mean(returns) / np.std(returns)
    
    # حساب أقصى انخفاض
    cumulative_returns = np.cumsum([r['return'] for r in self.performance_metrics['daily_returns']])
    running_max = np.maximum.accumulate(cumulative_returns)
    drawdown = cumulative_returns - running_max
    self.performance_metrics['max_drawdown'] = float(np.min(drawdown))
```

## 🚀 تحسين الأداء

### 1. تحسين الذاكرة:
```python
# استخدام numpy arrays بدلاً من lists
prices = np.array([c['close'] for c in candles])

# تنظيف البيانات القديمة
if len(self.market_data['candles'][asset]) > 200:
    self.market_data['candles'][asset] = self.market_data['candles'][asset][-100:]
```

### 2. معالجة متوازية:
```python
import asyncio

async def analyze_all_assets(self, assets):
    tasks = []
    for asset in assets:
        task = asyncio.create_task(self._analyze_asset(asset))
        tasks.append(task)
    
    results = await asyncio.gather(*tasks)
    return results
```

### 3. تخزين مؤقت ذكي:
```python
from functools import lru_cache

@lru_cache(maxsize=100)
def _calculate_indicator_cached(self, prices_hash, period):
    # حساب المؤشر مع تخزين مؤقت
    return self._calculate_indicator(prices, period)
```

## 🔍 اختبار وتصحيح الأخطاء

### 1. اختبارات الوحدة:
```python
import unittest

class TestTechnicalAnalysis(unittest.TestCase):
    def setUp(self):
        self.engine = TechnicalAnalysisEngine()
        self.sample_candles = self._generate_sample_candles()
    
    def test_ema_crossover(self):
        indicators = {'ema_5': [1.1, 1.2], 'ema_10': [1.0, 1.1]}
        result = self.engine._analyze_ema_crossover(self.sample_candles, indicators)
        self.assertIsNotNone(result)
        self.assertEqual(result.direction, TrendDirection.BULLISH)
```

### 2. تسجيل مفصل:
```python
import logging

logger = logging.getLogger(__name__)

def analyze_market(self, candles, indicators):
    logger.info(f"بدء تحليل السوق - عدد الشموع: {len(candles)}")
    
    try:
        # التحليل
        result = self._perform_analysis(candles, indicators)
        logger.info(f"تم التحليل بنجاح - النتيجة: {result}")
        return result
    except Exception as e:
        logger.error(f"خطأ في التحليل: {e}", exc_info=True)
        return None
```

### 3. مراقبة الأداء:
```python
import time
from functools import wraps

def performance_monitor(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        execution_time = end_time - start_time
        logger.debug(f"{func.__name__} استغرق {execution_time:.3f} ثانية")
        
        return result
    return wrapper
```

## 📈 مقاييس الجودة

### 1. مقاييس دقة التنبؤ:
- **Accuracy**: نسبة التنبؤات الصحيحة
- **Precision**: نسبة الإشارات الإيجابية الصحيحة
- **Recall**: نسبة الفرص المكتشفة
- **F1-Score**: المتوسط التوافقي للدقة والاستدعاء

### 2. مقاييس الأداء المالي:
- **Sharpe Ratio**: نسبة العائد إلى المخاطر
- **Maximum Drawdown**: أقصى انخفاض
- **Win Rate**: معدل النجاح
- **Profit Factor**: نسبة الأرباح إلى الخسائر

### 3. مقاييس الكفاءة:
- **Execution Time**: وقت التنفيذ
- **Memory Usage**: استخدام الذاكرة
- **CPU Usage**: استخدام المعالج
- **Network Latency**: زمن الاستجابة
