"""
نظام إدارة جلسات التداول للخيارات الثنائية
يدير الجلسات، الأصول، عدد الصفقات، والمبالغ
"""

import time
import json
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
from enum import Enum

from smart_trading_strategy import SmartTradingStrategy, SignalType

logger = logging.getLogger(__name__)

class SessionStatus(Enum):
    """حالة الجلسة"""
    WAITING = "waiting"        # في انتظار البدء
    ACTIVE = "active"          # نشطة
    PAUSED = "paused"          # متوقفة مؤقتاً
    COMPLETED = "completed"    # مكتملة
    STOPPED = "stopped"        # متوقفة

class TradeResult(Enum):
    """نتيجة الصفقة"""
    WIN = "win"
    LOSS = "loss"
    PENDING = "pending"

class TradingSessionManager:
    """مدير جلسات التداول"""
    
    def __init__(self, client, account_manager):
        self.client = client
        self.account_manager = account_manager
        self.strategy = SmartTradingStrategy()
        
        # إعدادات الجلسة
        self.session_config = {
            'asset': 'EURUSD',           # الزوج المختار
            'max_trades': 10,            # عدد الصفقات المسموح
            'trade_amount': 1.0,         # مبلغ الصفقة
            'account_type': 'demo',      # نوع الحساب
            'session_duration': 3600,    # مدة الجلسة بالثواني (ساعة)
            'min_interval': 60,          # الحد الأدنى بين الصفقات (ثانية)
        }
        
        # حالة الجلسة
        self.session_status = SessionStatus.WAITING
        self.session_start_time = None
        self.session_end_time = None
        
        # إحصائيات الجلسة
        self.session_stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'pending_trades': 0,
            'total_profit': 0.0,
            'win_rate': 0.0,
            'last_trade_time': 0
        }
        
        # قائمة الصفقات
        self.trades_history = []
        
        # مجلد حفظ البيانات
        self.sessions_dir = Path("data/sessions")
        self.sessions_dir.mkdir(parents=True, exist_ok=True)
        
    def configure_session(self, config: Dict[str, Any]) -> bool:
        """تكوين إعدادات الجلسة"""
        try:
            # التحقق من صحة الإعدادات
            if not self._validate_config(config):
                return False
            
            # تحديث الإعدادات
            self.session_config.update(config)
            
            logger.info(f"✅ تم تكوين الجلسة: {self.session_config}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تكوين الجلسة: {e}")
            return False
    
    def _validate_config(self, config: Dict[str, Any]) -> bool:
        """التحقق من صحة إعدادات الجلسة"""
        try:
            # التحقق من الزوج
            if 'asset' in config and not isinstance(config['asset'], str):
                logger.error("اسم الزوج يجب أن يكون نص")
                return False
            
            # التحقق من عدد الصفقات
            if 'max_trades' in config:
                if not isinstance(config['max_trades'], int) or config['max_trades'] <= 0:
                    logger.error("عدد الصفقات يجب أن يكون رقم صحيح موجب")
                    return False
            
            # التحقق من مبلغ الصفقة
            if 'trade_amount' in config:
                if not isinstance(config['trade_amount'], (int, float)) or config['trade_amount'] <= 0:
                    logger.error("مبلغ الصفقة يجب أن يكون رقم موجب")
                    return False
            
            # التحقق من نوع الحساب
            if 'account_type' in config:
                if config['account_type'] not in ['demo', 'real']:
                    logger.error("نوع الحساب يجب أن يكون demo أو real")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من الإعدادات: {e}")
            return False
    
    async def start_session(self) -> bool:
        """بدء جلسة التداول"""
        try:
            if self.session_status != SessionStatus.WAITING:
                logger.warning("الجلسة قيد التشغيل بالفعل أو مكتملة")
                return False
            
            # التبديل إلى نوع الحساب المطلوب
            if not await self.account_manager.switch_account(self.session_config['account_type']):
                logger.error("فشل في التبديل إلى نوع الحساب المطلوب")
                return False
            
            # تهيئة الجلسة
            self.session_status = SessionStatus.ACTIVE
            self.session_start_time = time.time()
            self.session_end_time = self.session_start_time + self.session_config['session_duration']
            
            # إعادة تعيين الإحصائيات
            self.session_stats = {
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'pending_trades': 0,
                'total_profit': 0.0,
                'win_rate': 0.0,
                'last_trade_time': 0
            }
            
            self.trades_history = []
            
            logger.info(f"🚀 بدء جلسة التداول على {self.session_config['asset']}")
            logger.info(f"📊 إعدادات الجلسة: {self.session_config}")
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في بدء الجلسة: {e}")
            return False
    
    async def process_trading_signal(self, asset_name: str, candles: List[Dict], indicators: Dict) -> Optional[Dict]:
        """معالجة إشارة التداول"""
        try:
            # التحقق من حالة الجلسة
            if self.session_status != SessionStatus.ACTIVE:
                return None
            
            # التحقق من انتهاء الجلسة
            if time.time() > self.session_end_time:
                await self.stop_session("انتهت مدة الجلسة")
                return None
            
            # التحقق من الزوج المطلوب
            if asset_name != self.session_config['asset']:
                return None
            
            # التحقق من عدد الصفقات
            if self.session_stats['total_trades'] >= self.session_config['max_trades']:
                await self.stop_session("تم الوصول للحد الأقصى من الصفقات")
                return None
            
            # التحقق من الفترة الزمنية بين الصفقات
            current_time = time.time()
            if (current_time - self.session_stats['last_trade_time']) < self.session_config['min_interval']:
                return None
            
            # تحليل السوق
            analysis_result = self.strategy.analyze_market(candles, indicators)
            
            # التحقق من صحة الإشارة
            if not self.strategy.should_trade(analysis_result):
                return None
            
            # تنفيذ الصفقة
            trade_result = await self._execute_trade(analysis_result)
            
            if trade_result:
                # تحديث الإحصائيات
                self._update_session_stats(trade_result)
                
                # حفظ الصفقة
                self.trades_history.append(trade_result)
                
                # حفظ بيانات الجلسة
                await self._save_session_data()
                
                logger.info(f"✅ تم تنفيذ صفقة {trade_result['direction']} على {asset_name}")
                logger.info(f"📊 إحصائيات الجلسة: {self.get_session_summary()}")
                
                return trade_result
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في معالجة إشارة التداول: {e}")
            return None
    
    async def _execute_trade(self, analysis_result: Dict) -> Optional[Dict]:
        """تنفيذ الصفقة"""
        try:
            # تحديد اتجاه الصفقة
            direction = "call" if analysis_result['signal'] == SignalType.CALL else "put"
            
            # تحديد مدة الصفقة
            duration = analysis_result['timeframe']
            
            # تنفيذ الصفقة
            trade_id = await self.client.buy_binary_option(
                asset=self.session_config['asset'],
                amount=self.session_config['trade_amount'],
                direction=direction,
                duration=duration
            )
            
            if trade_id:
                trade_data = {
                    'id': trade_id,
                    'asset': self.session_config['asset'],
                    'direction': direction,
                    'amount': self.session_config['trade_amount'],
                    'duration': duration,
                    'confidence': analysis_result['confidence'],
                    'open_time': time.time(),
                    'close_time': time.time() + duration,
                    'status': TradeResult.PENDING,
                    'profit': 0.0,
                    'analysis_details': analysis_result.get('analysis_details', {}),
                    'datetime': datetime.now().isoformat()
                }
                
                return trade_data
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في تنفيذ الصفقة: {e}")
            return None
    
    def _update_session_stats(self, trade_data: Dict):
        """تحديث إحصائيات الجلسة"""
        try:
            self.session_stats['total_trades'] += 1
            self.session_stats['last_trade_time'] = time.time()
            
            if trade_data['status'] == TradeResult.PENDING:
                self.session_stats['pending_trades'] += 1
            elif trade_data['status'] == TradeResult.WIN:
                self.session_stats['winning_trades'] += 1
                self.session_stats['total_profit'] += trade_data.get('profit', 0)
            elif trade_data['status'] == TradeResult.LOSS:
                self.session_stats['losing_trades'] += 1
                self.session_stats['total_profit'] += trade_data.get('profit', 0)  # الخسارة ستكون سالبة
            
            # حساب معدل النجاح
            completed_trades = self.session_stats['winning_trades'] + self.session_stats['losing_trades']
            if completed_trades > 0:
                self.session_stats['win_rate'] = (self.session_stats['winning_trades'] / completed_trades) * 100
            
        except Exception as e:
            logger.error(f"خطأ في تحديث إحصائيات الجلسة: {e}")
    
    async def _save_session_data(self):
        """حفظ بيانات الجلسة"""
        try:
            session_data = {
                'config': self.session_config,
                'status': self.session_status.value,
                'start_time': self.session_start_time,
                'end_time': self.session_end_time,
                'stats': self.session_stats,
                'trades': self.trades_history,
                'last_update': datetime.now().isoformat()
            }
            
            # اسم الملف بناءً على وقت البدء
            if self.session_start_time:
                session_date = datetime.fromtimestamp(self.session_start_time).strftime("%Y%m%d_%H%M%S")
                filename = f"session_{session_date}_{self.session_config['asset']}.json"
            else:
                filename = f"session_current_{self.session_config['asset']}.json"
            
            file_path = self.sessions_dir / filename
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات الجلسة: {e}")
    
    async def stop_session(self, reason: str = "إيقاف يدوي"):
        """إيقاف الجلسة"""
        try:
            if self.session_status == SessionStatus.ACTIVE:
                self.session_status = SessionStatus.STOPPED
                
                # حفظ البيانات النهائية
                await self._save_session_data()
                
                logger.info(f"⏹️ تم إيقاف الجلسة: {reason}")
                logger.info(f"📊 الإحصائيات النهائية: {self.get_session_summary()}")
            
        except Exception as e:
            logger.error(f"خطأ في إيقاف الجلسة: {e}")
    
    def get_session_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص الجلسة"""
        return {
            'config': self.session_config,
            'status': self.session_status.value,
            'stats': self.session_stats,
            'duration': time.time() - self.session_start_time if self.session_start_time else 0,
            'remaining_trades': self.session_config['max_trades'] - self.session_stats['total_trades'],
            'strategy_performance': self.strategy.get_performance_stats()
        }
    
    def is_session_active(self) -> bool:
        """التحقق من نشاط الجلسة"""
        return self.session_status == SessionStatus.ACTIVE
