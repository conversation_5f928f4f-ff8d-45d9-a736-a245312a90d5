"""
محرك التحليل الكمي المتقدم للسكالبينغ الاحترافي
يتضمن Z-Score، Probabilistic Filters، Correlation Matrix، وVolatility Filters
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score
import logging

logger = logging.getLogger(__name__)

@dataclass
class QuantitativeSignal:
    """إشارة كمية"""
    metric: str
    value: float
    z_score: float
    percentile: float
    confidence: float
    interpretation: str
    details: Dict[str, Any]

class QuantitativeAnalysisEngine:
    """محرك التحليل الكمي المتقدم"""
    
    def __init__(self):
        # معايير التحليل الكمي
        self.z_score_thresholds = {
            'extreme': 2.5,
            'strong': 2.0,
            'moderate': 1.5,
            'weak': 1.0
        }
        
        # فلاتر الاحتمالية
        self.probability_filters = {
            'min_sample_size': 30,
            'min_success_rate': 0.65,
            'confidence_level': 0.8
        }
        
        # عتبات التقلبات
        self.volatility_thresholds = {
            'very_low': 0.005,
            'low': 0.01,
            'normal': 0.02,
            'high': 0.04,
            'very_high': 0.08
        }
        
        # تاريخ الإشارات للتحليل الاحتمالي
        self.signal_history = []
        
    def analyze_market(self, candles: List[Dict], indicators: Dict, 
                      historical_signals: List[Dict] = None) -> Dict[str, Any]:
        """التحليل الكمي الشامل للسوق"""
        try:
            if len(candles) < 30:
                return self._get_insufficient_data_result()
            
            # تحويل البيانات إلى DataFrame
            df = self._prepare_dataframe(candles, indicators)
            
            # التحليلات الكمية
            analyses = {}
            
            # 1. تحليل Z-Score للانحرافات السعرية
            zscore_analysis = self._analyze_zscore_deviations(df)
            analyses['zscore'] = zscore_analysis
            
            # 2. فلاتر الاحتمالية
            probability_analysis = self._analyze_probability_filters(df, historical_signals)
            analyses['probability'] = probability_analysis
            
            # 3. مصفوفة الارتباط
            correlation_analysis = self._analyze_correlation_matrix(df)
            analyses['correlation'] = correlation_analysis
            
            # 4. فلاتر التقلبات
            volatility_analysis = self._analyze_volatility_filters(df)
            analyses['volatility'] = volatility_analysis
            
            # 5. تحليل الشارب ريشيو
            sharpe_analysis = self._analyze_sharpe_ratio(df)
            analyses['sharpe'] = sharpe_analysis
            
            # 6. تحليل الانحدار والاتجاه
            regression_analysis = self._analyze_regression_trend(df)
            analyses['regression'] = regression_analysis
            
            # دمج النتائج
            final_analysis = self._merge_quantitative_results(analyses, df)
            
            return final_analysis
            
        except Exception as e:
            logger.error(f"خطأ في التحليل الكمي: {e}")
            return self._get_error_result()
    
    def _prepare_dataframe(self, candles: List[Dict], indicators: Dict) -> pd.DataFrame:
        """تحضير DataFrame للتحليل الكمي"""
        try:
            df = pd.DataFrame(candles)
            
            # إضافة المؤشرات
            for indicator, values in indicators.items():
                if isinstance(values, list) and len(values) > 0:
                    padded_values = [None] * (len(df) - len(values)) + values
                    df[indicator] = padded_values
                elif isinstance(values, dict):
                    for key, value_list in values.items():
                        if isinstance(value_list, list) and len(value_list) > 0:
                            padded_values = [None] * (len(df) - len(value_list)) + value_list
                            df[f"{indicator}_{key}"] = padded_values
            
            # حساب مقاييس إضافية
            df['returns'] = df['close'].pct_change()
            df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
            df['volatility'] = df['returns'].rolling(window=10).std()
            df['price_momentum'] = df['close'] / df['close'].shift(5) - 1
            df['volume_momentum'] = df.get('volume', pd.Series([0]*len(df))).pct_change()
            
            return df
            
        except Exception as e:
            logger.error(f"خطأ في تحضير البيانات الكمية: {e}")
            return pd.DataFrame()
    
    def _analyze_zscore_deviations(self, df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل انحرافات Z-Score"""
        try:
            results = {}
            
            # تحليل Z-Score للأسعار
            if 'close' in df.columns:
                price_zscore = self._calculate_zscore(df['close'], window=20)
                current_zscore = price_zscore.iloc[-1] if not price_zscore.empty else 0
                
                results['price_zscore'] = {
                    'current_value': current_zscore,
                    'interpretation': self._interpret_zscore(current_zscore),
                    'signal_strength': self._zscore_to_strength(current_zscore),
                    'percentile': self._zscore_to_percentile(current_zscore)
                }
            
            # تحليل Z-Score للحجم
            if 'volume' in df.columns and not df['volume'].isna().all():
                volume_zscore = self._calculate_zscore(df['volume'], window=20)
                current_vol_zscore = volume_zscore.iloc[-1] if not volume_zscore.empty else 0
                
                results['volume_zscore'] = {
                    'current_value': current_vol_zscore,
                    'interpretation': self._interpret_zscore(current_vol_zscore),
                    'signal_strength': self._zscore_to_strength(current_vol_zscore)
                }
            
            # تحليل Z-Score للمؤشرات
            for indicator in ['rsi_5', 'momentum_10']:
                if indicator in df.columns:
                    indicator_zscore = self._calculate_zscore(df[indicator], window=20)
                    current_ind_zscore = indicator_zscore.iloc[-1] if not indicator_zscore.empty else 0
                    
                    results[f'{indicator}_zscore'] = {
                        'current_value': current_ind_zscore,
                        'interpretation': self._interpret_zscore(current_ind_zscore),
                        'signal_strength': self._zscore_to_strength(current_ind_zscore)
                    }
            
            # تقييم إجمالي لانحرافات Z-Score
            zscore_values = [r.get('current_value', 0) for r in results.values()]
            extreme_zscores = [z for z in zscore_values if abs(z) > self.z_score_thresholds['strong']]
            
            results['overall_assessment'] = {
                'extreme_deviations_count': len(extreme_zscores),
                'max_absolute_zscore': max([abs(z) for z in zscore_values]) if zscore_values else 0,
                'market_anomaly_detected': len(extreme_zscores) >= 2,
                'confidence': min(len(extreme_zscores) * 0.3, 1.0)
            }
            
            return results
            
        except Exception as e:
            logger.error(f"خطأ في تحليل Z-Score: {e}")
            return {}
    
    def _calculate_zscore(self, series: pd.Series, window: int = 20) -> pd.Series:
        """حساب Z-Score المتحرك"""
        try:
            rolling_mean = series.rolling(window=window).mean()
            rolling_std = series.rolling(window=window).std()
            
            # تجنب القسمة على صفر
            rolling_std = rolling_std.replace(0, np.nan)
            
            zscore = (series - rolling_mean) / rolling_std
            return zscore.fillna(0)
            
        except Exception:
            return pd.Series([0] * len(series))
    
    def _interpret_zscore(self, zscore: float) -> str:
        """تفسير قيمة Z-Score"""
        abs_zscore = abs(zscore)
        
        if abs_zscore >= self.z_score_thresholds['extreme']:
            return 'extreme_deviation'
        elif abs_zscore >= self.z_score_thresholds['strong']:
            return 'strong_deviation'
        elif abs_zscore >= self.z_score_thresholds['moderate']:
            return 'moderate_deviation'
        elif abs_zscore >= self.z_score_thresholds['weak']:
            return 'weak_deviation'
        else:
            return 'normal_range'
    
    def _zscore_to_strength(self, zscore: float) -> float:
        """تحويل Z-Score إلى قوة إشارة"""
        abs_zscore = abs(zscore)
        return min(abs_zscore / self.z_score_thresholds['extreme'], 1.0)
    
    def _zscore_to_percentile(self, zscore: float) -> float:
        """تحويل Z-Score إلى نسبة مئوية"""
        return stats.norm.cdf(zscore) * 100
    
    def _analyze_probability_filters(self, df: pd.DataFrame, 
                                   historical_signals: List[Dict] = None) -> Dict[str, Any]:
        """تحليل الفلاتر الاحتمالية"""
        try:
            results = {}
            
            if not historical_signals or len(historical_signals) < self.probability_filters['min_sample_size']:
                return {
                    'insufficient_history': True,
                    'sample_size': len(historical_signals) if historical_signals else 0,
                    'required_size': self.probability_filters['min_sample_size']
                }
            
            # تحليل معدل النجاح التاريخي
            success_rate = self._calculate_historical_success_rate(historical_signals)
            results['historical_success_rate'] = success_rate
            
            # تحليل الأنماط الناجحة
            pattern_analysis = self._analyze_successful_patterns(historical_signals, df)
            results['pattern_analysis'] = pattern_analysis
            
            # حساب الاحتمالية المشروطة
            conditional_probability = self._calculate_conditional_probability(historical_signals, df)
            results['conditional_probability'] = conditional_probability
            
            # فلتر الثقة الإحصائية
            confidence_filter = self._apply_confidence_filter(historical_signals)
            results['confidence_filter'] = confidence_filter
            
            # تقييم إجمالي للاحتمالية
            overall_probability = self._calculate_overall_probability(results)
            results['overall_probability'] = overall_probability
            
            return results
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الفلاتر الاحتمالية: {e}")
            return {}
    
    def _calculate_historical_success_rate(self, signals: List[Dict]) -> Dict[str, Any]:
        """حساب معدل النجاح التاريخي"""
        try:
            total_signals = len(signals)
            successful_signals = sum(1 for s in signals if s.get('result') == 'win')
            
            success_rate = successful_signals / total_signals if total_signals > 0 else 0
            
            # حساب فترة الثقة
            confidence_interval = self._calculate_confidence_interval(successful_signals, total_signals)
            
            return {
                'success_rate': success_rate,
                'total_signals': total_signals,
                'successful_signals': successful_signals,
                'confidence_interval': confidence_interval,
                'meets_threshold': success_rate >= self.probability_filters['min_success_rate']
            }
            
        except Exception:
            return {'success_rate': 0, 'meets_threshold': False}
    
    def _calculate_confidence_interval(self, successes: int, total: int, 
                                     confidence_level: float = 0.95) -> Tuple[float, float]:
        """حساب فترة الثقة لمعدل النجاح"""
        try:
            if total == 0:
                return (0.0, 0.0)
            
            p = successes / total
            z_score = stats.norm.ppf((1 + confidence_level) / 2)
            margin_error = z_score * np.sqrt(p * (1 - p) / total)
            
            lower_bound = max(0, p - margin_error)
            upper_bound = min(1, p + margin_error)
            
            return (lower_bound, upper_bound)
            
        except Exception:
            return (0.0, 0.0)

    def _analyze_successful_patterns(self, signals: List[Dict], df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل الأنماط الناجحة"""
        try:
            successful_signals = [s for s in signals if s.get('result') == 'win']

            if not successful_signals:
                return {'no_successful_patterns': True}

            # تحليل أنماط الوقت
            time_patterns = self._analyze_time_patterns(successful_signals)

            # تحليل أنماط المؤشرات
            indicator_patterns = self._analyze_indicator_patterns(successful_signals)

            # تحليل أنماط التقلبات
            volatility_patterns = self._analyze_volatility_patterns(successful_signals)

            return {
                'time_patterns': time_patterns,
                'indicator_patterns': indicator_patterns,
                'volatility_patterns': volatility_patterns,
                'pattern_confidence': self._calculate_pattern_confidence(successful_signals)
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل الأنماط: {e}")
            return {}

    def _analyze_correlation_matrix(self, df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل مصفوفة الارتباط"""
        try:
            # اختيار المتغيرات للتحليل
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            analysis_columns = [col for col in numeric_columns if col in [
                'close', 'returns', 'volatility', 'rsi_5', 'rsi_14',
                'ema_5', 'ema_10', 'momentum_10', 'volume'
            ]]

            if len(analysis_columns) < 3:
                return {'insufficient_variables': True}

            # حساب مصفوفة الارتباط
            correlation_matrix = df[analysis_columns].corr()

            # تحليل الارتباطات القوية
            strong_correlations = self._find_strong_correlations(correlation_matrix)

            # تحليل الارتباط مع العوائد
            returns_correlations = self._analyze_returns_correlations(correlation_matrix)

            # كشف الارتباطات الشاذة
            anomalous_correlations = self._detect_anomalous_correlations(correlation_matrix)

            return {
                'correlation_matrix': correlation_matrix.to_dict(),
                'strong_correlations': strong_correlations,
                'returns_correlations': returns_correlations,
                'anomalous_correlations': anomalous_correlations,
                'market_coherence': self._calculate_market_coherence(correlation_matrix)
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل الارتباط: {e}")
            return {}

    def _analyze_volatility_filters(self, df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل فلاتر التقلبات"""
        try:
            results = {}

            # تحليل التقلبات الحالية
            current_volatility = df['volatility'].iloc[-1] if 'volatility' in df.columns else 0

            # تصنيف مستوى التقلبات
            volatility_level = self._classify_volatility_level(current_volatility)
            results['current_volatility'] = {
                'value': current_volatility,
                'level': volatility_level,
                'percentile': self._volatility_to_percentile(df['volatility'], current_volatility)
            }

            # تحليل اتجاه التقلبات
            volatility_trend = self._analyze_volatility_trend(df['volatility'])
            results['volatility_trend'] = volatility_trend

            # فلتر التداول بناءً على التقلبات
            trading_filter = self._apply_volatility_trading_filter(current_volatility, volatility_level)
            results['trading_filter'] = trading_filter

            # تحليل نسبة المخاطر/العوائد
            risk_reward_analysis = self._analyze_risk_reward_ratio(df)
            results['risk_reward'] = risk_reward_analysis

            return results

        except Exception as e:
            logger.error(f"خطأ في تحليل التقلبات: {e}")
            return {}

    def _analyze_sharpe_ratio(self, df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل نسبة شارب"""
        try:
            if 'returns' not in df.columns or df['returns'].isna().all():
                return {'insufficient_data': True}

            # حساب نسبة شارب المتحركة
            rolling_sharpe = self._calculate_rolling_sharpe(df['returns'])
            current_sharpe = rolling_sharpe.iloc[-1] if not rolling_sharpe.empty else 0

            # تفسير نسبة شارب
            sharpe_interpretation = self._interpret_sharpe_ratio(current_sharpe)

            # تحليل اتجاه نسبة شارب
            sharpe_trend = self._analyze_sharpe_trend(rolling_sharpe)

            return {
                'current_sharpe': current_sharpe,
                'interpretation': sharpe_interpretation,
                'trend': sharpe_trend,
                'rolling_values': rolling_sharpe.tail(10).tolist(),
                'quality_score': self._sharpe_to_quality_score(current_sharpe)
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل شارب: {e}")
            return {}

    def _analyze_regression_trend(self, df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل الانحدار والاتجاه"""
        try:
            if len(df) < 10:
                return {'insufficient_data': True}

            # تحليل الانحدار الخطي للأسعار
            price_regression = self._calculate_linear_regression(df['close'])

            # تحليل الانحدار للحجم
            volume_regression = None
            if 'volume' in df.columns and not df['volume'].isna().all():
                volume_regression = self._calculate_linear_regression(df['volume'])

            # تحليل قوة الاتجاه
            trend_strength = self._calculate_trend_strength(df['close'])

            # كشف نقاط التحول
            turning_points = self._detect_turning_points(df['close'])

            return {
                'price_regression': price_regression,
                'volume_regression': volume_regression,
                'trend_strength': trend_strength,
                'turning_points': turning_points,
                'trend_reliability': self._assess_trend_reliability(price_regression, trend_strength)
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل الانحدار: {e}")
            return {}

    def _classify_volatility_level(self, volatility: float) -> str:
        """تصنيف مستوى التقلبات"""
        if volatility <= self.volatility_thresholds['very_low']:
            return 'very_low'
        elif volatility <= self.volatility_thresholds['low']:
            return 'low'
        elif volatility <= self.volatility_thresholds['normal']:
            return 'normal'
        elif volatility <= self.volatility_thresholds['high']:
            return 'high'
        else:
            return 'very_high'

    def _calculate_rolling_sharpe(self, returns: pd.Series, window: int = 20,
                                 risk_free_rate: float = 0.0) -> pd.Series:
        """حساب نسبة شارب المتحركة"""
        try:
            excess_returns = returns - risk_free_rate
            rolling_mean = excess_returns.rolling(window=window).mean()
            rolling_std = returns.rolling(window=window).std()

            # تجنب القسمة على صفر
            rolling_std = rolling_std.replace(0, np.nan)

            sharpe_ratio = rolling_mean / rolling_std
            return sharpe_ratio.fillna(0)

        except Exception:
            return pd.Series([0] * len(returns))

    def _interpret_sharpe_ratio(self, sharpe: float) -> str:
        """تفسير نسبة شارب"""
        if sharpe >= 2.0:
            return 'excellent'
        elif sharpe >= 1.0:
            return 'good'
        elif sharpe >= 0.5:
            return 'acceptable'
        elif sharpe >= 0.0:
            return 'poor'
        else:
            return 'very_poor'

    def _calculate_linear_regression(self, series: pd.Series, window: int = 20) -> Dict[str, Any]:
        """حساب الانحدار الخطي"""
        try:
            if len(series) < window:
                return {'insufficient_data': True}

            recent_data = series.tail(window)
            x = np.arange(len(recent_data))
            y = recent_data.values

            # حساب الانحدار
            slope, intercept, r_value, p_value, std_err = stats.linregress(x, y)

            return {
                'slope': slope,
                'intercept': intercept,
                'r_squared': r_value ** 2,
                'p_value': p_value,
                'trend_direction': 'upward' if slope > 0 else 'downward',
                'trend_significance': p_value < 0.05,
                'strength': abs(r_value)
            }

        except Exception:
            return {'error': True}

    def _merge_quantitative_results(self, analyses: Dict[str, Any], df: pd.DataFrame) -> Dict[str, Any]:
        """دمج نتائج التحليل الكمي"""
        try:
            # حساب النتيجة الكمية الإجمالية
            quantitative_score = self._calculate_quantitative_score(analyses)

            # تحديد قوة الإشارة الكمية
            signal_strength = self._determine_quantitative_strength(analyses)

            # تحليل جودة البيانات
            data_quality = self._assess_data_quality(df, analyses)

            # توصيات التداول الكمية
            trading_recommendations = self._generate_trading_recommendations(analyses)

            # تحليل المخاطر الكمية
            risk_assessment = self._assess_quantitative_risks(analyses)

            return {
                'signal_type': 'QUANTITATIVE_ANALYSIS',
                'overall_score': quantitative_score,
                'signal_strength': signal_strength,
                'data_quality': data_quality,
                'trading_recommendations': trading_recommendations,
                'risk_assessment': risk_assessment,
                'detailed_analyses': analyses,
                'confidence': self._calculate_overall_confidence(analyses),
                'timestamp': pd.Timestamp.now().isoformat()
            }

        except Exception as e:
            logger.error(f"خطأ في دمج النتائج الكمية: {e}")
            return self._get_error_result()

    def _calculate_quantitative_score(self, analyses: Dict[str, Any]) -> float:
        """حساب النتيجة الكمية الإجمالية"""
        try:
            scores = []
            weights = {
                'zscore': 0.25,
                'probability': 0.30,
                'correlation': 0.15,
                'volatility': 0.15,
                'sharpe': 0.10,
                'regression': 0.05
            }

            # نتيجة Z-Score
            if 'zscore' in analyses and 'overall_assessment' in analyses['zscore']:
                zscore_confidence = analyses['zscore']['overall_assessment'].get('confidence', 0)
                scores.append(zscore_confidence * weights['zscore'])

            # نتيجة الاحتمالية
            if 'probability' in analyses and 'overall_probability' in analyses['probability']:
                prob_score = analyses['probability']['overall_probability'].get('score', 0)
                scores.append(prob_score * weights['probability'])

            # نتيجة الارتباط
            if 'correlation' in analyses and 'market_coherence' in analyses['correlation']:
                corr_score = analyses['correlation']['market_coherence'].get('score', 0.5)
                scores.append(corr_score * weights['correlation'])

            # نتيجة التقلبات
            if 'volatility' in analyses and 'trading_filter' in analyses['volatility']:
                vol_score = 1.0 if analyses['volatility']['trading_filter'].get('recommended', False) else 0.3
                scores.append(vol_score * weights['volatility'])

            # نتيجة شارب
            if 'sharpe' in analyses and 'quality_score' in analyses['sharpe']:
                sharpe_score = analyses['sharpe']['quality_score']
                scores.append(sharpe_score * weights['sharpe'])

            # نتيجة الانحدار
            if 'regression' in analyses and 'trend_reliability' in analyses['regression']:
                regression_score = analyses['regression']['trend_reliability'].get('score', 0.5)
                scores.append(regression_score * weights['regression'])

            return sum(scores) if scores else 0.0

        except Exception:
            return 0.0

    def _determine_quantitative_strength(self, analyses: Dict[str, Any]) -> str:
        """تحديد قوة الإشارة الكمية"""
        try:
            strength_indicators = []

            # قوة Z-Score
            if 'zscore' in analyses:
                extreme_count = analyses['zscore'].get('overall_assessment', {}).get('extreme_deviations_count', 0)
                if extreme_count >= 2:
                    strength_indicators.append('strong')
                elif extreme_count >= 1:
                    strength_indicators.append('moderate')

            # قوة الاحتمالية
            if 'probability' in analyses:
                success_rate = analyses['probability'].get('historical_success_rate', {}).get('success_rate', 0)
                if success_rate >= 0.8:
                    strength_indicators.append('strong')
                elif success_rate >= 0.7:
                    strength_indicators.append('moderate')

            # قوة التقلبات
            if 'volatility' in analyses:
                vol_level = analyses['volatility'].get('current_volatility', {}).get('level', 'normal')
                if vol_level in ['normal', 'elevated']:
                    strength_indicators.append('moderate')

            # تحديد القوة الإجمالية
            strong_count = strength_indicators.count('strong')
            moderate_count = strength_indicators.count('moderate')

            if strong_count >= 2:
                return 'very_strong'
            elif strong_count >= 1 or moderate_count >= 3:
                return 'strong'
            elif moderate_count >= 2:
                return 'moderate'
            elif moderate_count >= 1:
                return 'weak'
            else:
                return 'very_weak'

        except Exception:
            return 'unknown'

    def _assess_data_quality(self, df: pd.DataFrame, analyses: Dict[str, Any]) -> Dict[str, Any]:
        """تقييم جودة البيانات"""
        try:
            quality_metrics = {}

            # اكتمال البيانات
            completeness = 1 - df.isnull().sum().sum() / (len(df) * len(df.columns))
            quality_metrics['completeness'] = completeness

            # حجم العينة
            sample_size_score = min(len(df) / 100, 1.0)  # أفضل عند 100+ شمعة
            quality_metrics['sample_size'] = sample_size_score

            # استقرار البيانات
            if 'volatility' in df.columns:
                volatility_stability = 1 - df['volatility'].std() / df['volatility'].mean() if df['volatility'].mean() > 0 else 0
                quality_metrics['stability'] = max(0, min(volatility_stability, 1))

            # جودة المؤشرات
            indicator_quality = self._assess_indicator_quality(analyses)
            quality_metrics['indicator_quality'] = indicator_quality

            # النتيجة الإجمالية
            overall_quality = np.mean(list(quality_metrics.values()))

            return {
                'overall_score': overall_quality,
                'metrics': quality_metrics,
                'grade': self._quality_score_to_grade(overall_quality)
            }

        except Exception:
            return {'overall_score': 0.5, 'grade': 'unknown'}

    def _generate_trading_recommendations(self, analyses: Dict[str, Any]) -> Dict[str, Any]:
        """توليد توصيات التداول الكمية"""
        try:
            recommendations = {}

            # توصيات بناءً على Z-Score
            if 'zscore' in analyses:
                zscore_rec = self._zscore_recommendations(analyses['zscore'])
                recommendations['zscore_based'] = zscore_rec

            # توصيات بناءً على الاحتمالية
            if 'probability' in analyses:
                prob_rec = self._probability_recommendations(analyses['probability'])
                recommendations['probability_based'] = prob_rec

            # توصيات بناءً على التقلبات
            if 'volatility' in analyses:
                vol_rec = self._volatility_recommendations(analyses['volatility'])
                recommendations['volatility_based'] = vol_rec

            # توصية إجمالية
            overall_recommendation = self._synthesize_recommendations(recommendations)

            return {
                'individual_recommendations': recommendations,
                'overall_recommendation': overall_recommendation,
                'confidence_level': self._calculate_recommendation_confidence(recommendations)
            }

        except Exception:
            return {'overall_recommendation': 'hold', 'confidence_level': 0}

    def _assess_quantitative_risks(self, analyses: Dict[str, Any]) -> Dict[str, Any]:
        """تقييم المخاطر الكمية"""
        try:
            risks = {}

            # مخاطر التقلبات
            if 'volatility' in analyses:
                vol_level = analyses['volatility'].get('current_volatility', {}).get('level', 'normal')
                risks['volatility_risk'] = {
                    'level': vol_level,
                    'risk_score': self._volatility_to_risk_score(vol_level)
                }

            # مخاطر عدم الاستقرار
            if 'correlation' in analyses:
                coherence = analyses['correlation'].get('market_coherence', {}).get('score', 0.5)
                risks['instability_risk'] = {
                    'coherence_score': coherence,
                    'risk_score': 1 - coherence
                }

            # مخاطر البيانات
            if 'probability' in analyses and 'insufficient_history' in analyses['probability']:
                risks['data_risk'] = {
                    'insufficient_history': True,
                    'risk_score': 0.8
                }

            # تقييم المخاطر الإجمالي
            risk_scores = [r.get('risk_score', 0.5) for r in risks.values()]
            overall_risk = np.mean(risk_scores) if risk_scores else 0.5

            return {
                'individual_risks': risks,
                'overall_risk_score': overall_risk,
                'risk_level': self._risk_score_to_level(overall_risk)
            }

        except Exception:
            return {'overall_risk_score': 0.5, 'risk_level': 'medium'}

    def _calculate_overall_confidence(self, analyses: Dict[str, Any]) -> float:
        """حساب الثقة الإجمالية"""
        try:
            confidence_factors = []

            # ثقة Z-Score
            if 'zscore' in analyses:
                zscore_conf = analyses['zscore'].get('overall_assessment', {}).get('confidence', 0)
                confidence_factors.append(zscore_conf)

            # ثقة الاحتمالية
            if 'probability' in analyses and 'overall_probability' in analyses['probability']:
                prob_conf = analyses['probability']['overall_probability'].get('confidence', 0)
                confidence_factors.append(prob_conf)

            # ثقة التقلبات
            if 'volatility' in analyses:
                vol_filter = analyses['volatility'].get('trading_filter', {})
                vol_conf = 0.8 if vol_filter.get('recommended', False) else 0.3
                confidence_factors.append(vol_conf)

            return np.mean(confidence_factors) if confidence_factors else 0.0

        except Exception:
            return 0.0

    def _get_insufficient_data_result(self) -> Dict[str, Any]:
        """نتيجة عدم كفاية البيانات"""
        return {
            'signal_type': 'QUANTITATIVE_ANALYSIS',
            'overall_score': 0,
            'signal_strength': 'insufficient_data',
            'confidence': 0,
            'reason': 'insufficient_data_for_quantitative_analysis',
            'timestamp': pd.Timestamp.now().isoformat()
        }

    def _get_error_result(self) -> Dict[str, Any]:
        """نتيجة الخطأ"""
        return {
            'signal_type': 'QUANTITATIVE_ANALYSIS',
            'overall_score': 0,
            'signal_strength': 'error',
            'confidence': 0,
            'reason': 'quantitative_analysis_error',
            'timestamp': pd.Timestamp.now().isoformat()
        }
