"""
التكامل المتقدم مع PyQuotex
يدمج استراتيجية السكالبينغ الاحترافية مع منصة Quotex
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

from quotexapi.stable_api import Quotex
from professional_scalping_strategy import ProfessionalScalpingStrategy, SignalDecision
from technical_analysis_engine import TechnicalAnalysisEngine

logger = logging.getLogger(__name__)

class AdvancedQuotexIntegration:
    """التكامل المتقدم مع Quotex"""
    
    def __init__(self, email: str, password: str):
        self.email = email
        self.password = password
        self.client = Quotex(email, password)
        
        # استراتيجية السكالبينغ الاحترافية
        self.strategy = ProfessionalScalpingStrategy()
        
        # إعدادات التداول
        self.trading_config = {
            'default_amount': 10,  # المبلغ الافتراضي
            'max_amount': 100,     # الحد الأقصى للمبلغ
            'min_amount': 1,       # الحد الأدنى للمبلغ
            'max_concurrent_trades': 3,  # الحد الأقصى للصفقات المتزامنة
            'risk_percentage': 0.02,      # نسبة المخاطرة من الرصيد
            'stop_loss_percentage': 0.1,  # نسبة وقف الخسارة
            'take_profit_percentage': 0.15 # نسبة جني الأرباح
        }
        
        # حالة التداول
        self.trading_state = {
            'is_connected': False,
            'is_trading_active': False,
            'active_trades': {},
            'balance': 0.0,
            'daily_profit': 0.0,
            'daily_trades': 0,
            'last_trade_time': None
        }
        
        # إحصائيات الأداء
        self.performance_metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'win_rate': 0.0,
            'total_profit': 0.0,
            'max_drawdown': 0.0,
            'sharpe_ratio': 0.0,
            'daily_returns': []
        }
        
        # بيانات السوق
        self.market_data = {
            'candles': {},
            'indicators': {},
            'last_update': None
        }
        
        # قائمة الأصول المدعومة
        self.supported_assets = [
            'EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD',
            'EURJPY', 'GBPJPY', 'EURGBP', 'AUDJPY', 'NZDUSD'
        ]
        
    async def initialize(self) -> bool:
        """تهيئة الاتصال مع Quotex"""
        try:
            logger.info("🔄 بدء الاتصال مع Quotex...")
            
            # الاتصال بـ Quotex
            check_connect, reason = await self.client.connect()
            
            if check_connect:
                logger.info("✅ تم الاتصال بنجاح مع Quotex")
                self.trading_state['is_connected'] = True
                
                # الحصول على معلومات الحساب
                await self._update_account_info()
                
                # تهيئة بيانات السوق
                await self._initialize_market_data()
                
                return True
            else:
                logger.error(f"❌ فشل الاتصال مع Quotex: {reason}")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة الاتصال: {e}")
            return False
    
    async def start_trading(self, assets: List[str] = None) -> bool:
        """بدء التداول الآلي"""
        try:
            if not self.trading_state['is_connected']:
                logger.error("❌ غير متصل بـ Quotex")
                return False
            
            if assets is None:
                assets = self.supported_assets[:5]  # أول 5 أصول
            
            logger.info(f"🚀 بدء التداول الآلي للأصول: {assets}")
            self.trading_state['is_trading_active'] = True
            
            # حلقة التداول الرئيسية
            while self.trading_state['is_trading_active']:
                try:
                    # تحديث بيانات السوق
                    await self._update_market_data(assets)
                    
                    # تحليل كل أصل
                    for asset in assets:
                        if not self.trading_state['is_trading_active']:
                            break
                        
                        await self._analyze_and_trade(asset)
                    
                    # مراقبة الصفقات النشطة
                    await self._monitor_active_trades()
                    
                    # تحديث الإحصائيات
                    await self._update_performance_metrics()
                    
                    # انتظار قبل التكرار التالي
                    await asyncio.sleep(5)  # تحديث كل 5 ثوانٍ
                    
                except Exception as e:
                    logger.error(f"❌ خطأ في حلقة التداول: {e}")
                    await asyncio.sleep(10)  # انتظار أطول عند الخطأ
            
            logger.info("⏹️ تم إيقاف التداول الآلي")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في بدء التداول: {e}")
            return False
    
    async def _analyze_and_trade(self, asset: str):
        """تحليل أصل واتخاذ قرار التداول"""
        try:
            # التحقق من إمكانية التداول
            if not self._can_trade(asset):
                return
            
            # الحصول على بيانات الأصل
            candles = self.market_data['candles'].get(asset, [])
            indicators = self.market_data['indicators'].get(asset, {})
            
            if len(candles) < 30:
                logger.debug(f"⚠️ بيانات غير كافية للأصل {asset}")
                return
            
            # التحليل الشامل
            signal = self.strategy.analyze_market_comprehensive(
                candles=candles,
                indicators=indicators,
                volume_data=None,  # Quotex لا يوفر بيانات الحجم
                historical_signals=None  # يمكن إضافة قاعدة بيانات للإشارات التاريخية
            )
            
            if signal is None:
                logger.debug(f"📊 لا توجد إشارة للأصل {asset}")
                return
            
            # تنفيذ الصفقة
            await self._execute_trade(asset, signal)
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحليل الأصل {asset}: {e}")
    
    async def _execute_trade(self, asset: str, signal):
        """تنفيذ الصفقة"""
        try:
            # تحديد نوع الصفقة
            if signal.decision in [SignalDecision.STRONG_CALL, SignalDecision.CALL, SignalDecision.WEAK_CALL]:
                direction = "call"
            elif signal.decision in [SignalDecision.STRONG_PUT, SignalDecision.PUT, SignalDecision.WEAK_PUT]:
                direction = "put"
            else:
                return  # HOLD
            
            # حساب مبلغ الصفقة
            trade_amount = self._calculate_trade_amount(signal)
            
            # تحديد مدة الصفقة
            duration = signal.timeframe
            
            logger.info(f"📈 تنفيذ صفقة {direction.upper()} على {asset}")
            logger.info(f"💰 المبلغ: ${trade_amount} | ⏱️ المدة: {duration}s | 🎯 الثقة: {signal.confidence:.1%}")
            
            # تنفيذ الصفقة عبر Quotex
            success, trade_id = await self.client.buy(
                amount=trade_amount,
                asset=asset,
                direction=direction,
                duration=duration
            )
            
            if success:
                # حفظ معلومات الصفقة
                self.trading_state['active_trades'][trade_id] = {
                    'asset': asset,
                    'direction': direction,
                    'amount': trade_amount,
                    'duration': duration,
                    'signal': signal,
                    'start_time': datetime.now(),
                    'expected_end_time': datetime.now() + timedelta(seconds=duration)
                }
                
                self.trading_state['daily_trades'] += 1
                self.trading_state['last_trade_time'] = datetime.now()
                
                logger.info(f"✅ تم تنفيذ الصفقة بنجاح | ID: {trade_id}")
                
            else:
                logger.error(f"❌ فشل في تنفيذ الصفقة على {asset}")
                
        except Exception as e:
            logger.error(f"❌ خطأ في تنفيذ الصفقة: {e}")
    
    def _calculate_trade_amount(self, signal) -> float:
        """حساب مبلغ الصفقة بناءً على الإشارة والمخاطر"""
        try:
            # المبلغ الأساسي حسب قوة الإشارة
            if signal.decision in [SignalDecision.STRONG_CALL, SignalDecision.STRONG_PUT]:
                base_amount = self.trading_config['default_amount'] * 1.5
            elif signal.decision in [SignalDecision.CALL, SignalDecision.PUT]:
                base_amount = self.trading_config['default_amount']
            else:  # WEAK signals
                base_amount = self.trading_config['default_amount'] * 0.7
            
            # تعديل حسب مستوى الثقة
            confidence_multiplier = 0.5 + (signal.confidence * 0.5)
            adjusted_amount = base_amount * confidence_multiplier
            
            # تعديل حسب مستوى المخاطر
            if signal.risk_level == 'high':
                adjusted_amount *= 0.7
            elif signal.risk_level == 'low':
                adjusted_amount *= 1.2
            
            # تطبيق حدود المبلغ
            final_amount = max(
                self.trading_config['min_amount'],
                min(adjusted_amount, self.trading_config['max_amount'])
            )
            
            # التحقق من نسبة المخاطرة من الرصيد
            max_risk_amount = self.trading_state['balance'] * self.trading_config['risk_percentage']
            final_amount = min(final_amount, max_risk_amount)
            
            return round(final_amount, 2)
            
        except Exception:
            return self.trading_config['default_amount']
    
    def _can_trade(self, asset: str) -> bool:
        """التحقق من إمكانية التداول"""
        try:
            # التحقق من الاتصال
            if not self.trading_state['is_connected']:
                return False
            
            # التحقق من حالة التداول
            if not self.trading_state['is_trading_active']:
                return False
            
            # التحقق من عدد الصفقات المتزامنة
            active_count = len(self.trading_state['active_trades'])
            if active_count >= self.trading_config['max_concurrent_trades']:
                return False
            
            # التحقق من الرصيد
            min_balance = self.trading_config['min_amount'] * 10
            if self.trading_state['balance'] < min_balance:
                logger.warning(f"⚠️ الرصيد منخفض: ${self.trading_state['balance']}")
                return False
            
            # التحقق من وقت آخر صفقة (تجنب التداول المفرط)
            if self.trading_state['last_trade_time']:
                time_since_last = datetime.now() - self.trading_state['last_trade_time']
                if time_since_last.total_seconds() < 30:  # 30 ثانية على الأقل بين الصفقات
                    return False
            
            # التحقق من مناسبة وقت التداول
            if not self.strategy.is_trading_time_suitable():
                return False
            
            return True
            
        except Exception:
            return False
    
    async def _monitor_active_trades(self):
        """مراقبة الصفقات النشطة"""
        try:
            completed_trades = []
            
            for trade_id, trade_info in self.trading_state['active_trades'].items():
                # التحقق من انتهاء الصفقة
                if datetime.now() >= trade_info['expected_end_time']:
                    # الحصول على نتيجة الصفقة
                    result = await self._get_trade_result(trade_id)
                    
                    if result is not None:
                        await self._process_trade_result(trade_id, trade_info, result)
                        completed_trades.append(trade_id)
            
            # إزالة الصفقات المكتملة
            for trade_id in completed_trades:
                del self.trading_state['active_trades'][trade_id]
                
        except Exception as e:
            logger.error(f"❌ خطأ في مراقبة الصفقات: {e}")
    
    async def _get_trade_result(self, trade_id: str) -> Optional[Dict]:
        """الحصول على نتيجة الصفقة"""
        try:
            # استعلام عن نتيجة الصفقة من Quotex
            result = await self.client.get_optioninfo(trade_id)
            return result
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على نتيجة الصفقة {trade_id}: {e}")
            return None
    
    async def _process_trade_result(self, trade_id: str, trade_info: Dict, result: Dict):
        """معالجة نتيجة الصفقة"""
        try:
            is_win = result.get('win', False)
            profit = result.get('profit', 0)
            
            # تحديث الإحصائيات
            self.performance_metrics['total_trades'] += 1
            
            if is_win:
                self.performance_metrics['winning_trades'] += 1
                self.trading_state['daily_profit'] += profit
                logger.info(f"🎉 صفقة رابحة | ID: {trade_id} | الربح: ${profit}")
                
                # تحديث نتيجة الإشارة في الاستراتيجية
                self.strategy.update_signal_result(trade_id, True)
                
            else:
                self.performance_metrics['losing_trades'] += 1
                self.trading_state['daily_profit'] -= trade_info['amount']
                logger.info(f"😞 صفقة خاسرة | ID: {trade_id} | الخسارة: ${trade_info['amount']}")
                
                # تحديث نتيجة الإشارة في الاستراتيجية
                self.strategy.update_signal_result(trade_id, False)
            
            # حساب معدل النجاح
            total_completed = self.performance_metrics['winning_trades'] + self.performance_metrics['losing_trades']
            if total_completed > 0:
                self.performance_metrics['win_rate'] = self.performance_metrics['winning_trades'] / total_completed
            
            # تحديث الربح الإجمالي
            self.performance_metrics['total_profit'] += profit if is_win else -trade_info['amount']
            
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة نتيجة الصفقة: {e}")
    
    async def stop_trading(self):
        """إيقاف التداول"""
        try:
            logger.info("⏹️ إيقاف التداول...")
            self.trading_state['is_trading_active'] = False
            
            # انتظار انتهاء الصفقات النشطة
            while self.trading_state['active_trades']:
                logger.info(f"⏳ انتظار انتهاء {len(self.trading_state['active_trades'])} صفقة نشطة...")
                await self._monitor_active_trades()
                await asyncio.sleep(5)
            
            logger.info("✅ تم إيقاف التداول بنجاح")
            
        except Exception as e:
            logger.error(f"❌ خطأ في إيقاف التداول: {e}")
    
    def get_trading_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص التداول"""
        return {
            'trading_state': self.trading_state.copy(),
            'performance_metrics': self.performance_metrics.copy(),
            'strategy_performance': self.strategy.get_performance_summary(),
            'timestamp': datetime.now().isoformat()
        }

    async def _update_account_info(self):
        """تحديث معلومات الحساب"""
        try:
            # الحصول على الرصيد
            balance = await self.client.get_balance()
            if balance:
                self.trading_state['balance'] = balance
                logger.info(f"💰 الرصيد الحالي: ${balance}")

        except Exception as e:
            logger.error(f"❌ خطأ في تحديث معلومات الحساب: {e}")

    async def _initialize_market_data(self):
        """تهيئة بيانات السوق"""
        try:
            for asset in self.supported_assets:
                self.market_data['candles'][asset] = []
                self.market_data['indicators'][asset] = {}

            logger.info("📊 تم تهيئة بيانات السوق")

        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة بيانات السوق: {e}")

    async def _update_market_data(self, assets: List[str]):
        """تحديث بيانات السوق"""
        try:
            for asset in assets:
                # الحصول على الشموع
                candles = await self._get_candles(asset, 100)  # آخر 100 شمعة
                if candles:
                    self.market_data['candles'][asset] = candles

                    # حساب المؤشرات الفنية
                    indicators = self._calculate_indicators(candles)
                    self.market_data['indicators'][asset] = indicators

            self.market_data['last_update'] = datetime.now()

        except Exception as e:
            logger.error(f"❌ خطأ في تحديث بيانات السوق: {e}")

    async def _get_candles(self, asset: str, count: int = 100) -> List[Dict]:
        """الحصول على شموع الأصل"""
        try:
            # الحصول على البيانات من Quotex
            candles_data = await self.client.get_candles(asset, 60, count)  # شموع دقيقة واحدة

            if not candles_data:
                return []

            # تحويل البيانات إلى التنسيق المطلوب
            candles = []
            for candle in candles_data:
                candles.append({
                    'open': candle['open'],
                    'high': candle['max'],
                    'low': candle['min'],
                    'close': candle['close'],
                    'timestamp': candle['at'],
                    'volume': 0  # Quotex لا يوفر بيانات الحجم
                })

            return candles

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على شموع {asset}: {e}")
            return []

    def _calculate_indicators(self, candles: List[Dict]) -> Dict[str, Any]:
        """حساب المؤشرات الفنية"""
        try:
            if len(candles) < 30:
                return {}

            # تحويل إلى DataFrame
            df = pd.DataFrame(candles)

            indicators = {}

            # RSI
            indicators['rsi_5'] = self._calculate_rsi(df['close'], 5)
            indicators['rsi_14'] = self._calculate_rsi(df['close'], 14)

            # EMA
            indicators['ema_5'] = self._calculate_ema(df['close'], 5)
            indicators['ema_10'] = self._calculate_ema(df['close'], 10)
            indicators['ema_21'] = self._calculate_ema(df['close'], 21)

            # MACD
            macd_data = self._calculate_macd(df['close'])
            indicators['macd'] = macd_data

            # Bollinger Bands
            bb_data = self._calculate_bollinger_bands(df['close'])
            indicators['bollinger_bands'] = bb_data

            # Momentum
            indicators['momentum_10'] = self._calculate_momentum(df['close'], 10)

            # Z-Score
            indicators['zscore_20'] = self._calculate_zscore(df['close'], 20)

            return indicators

        except Exception as e:
            logger.error(f"❌ خطأ في حساب المؤشرات: {e}")
            return {}

    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> float:
        """حساب RSI"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return float(rsi.iloc[-1]) if not rsi.empty else 50.0
        except Exception:
            return 50.0

    def _calculate_ema(self, prices: pd.Series, period: int) -> float:
        """حساب EMA"""
        try:
            ema = prices.ewm(span=period).mean()
            return float(ema.iloc[-1]) if not ema.empty else float(prices.iloc[-1])
        except Exception:
            return float(prices.iloc[-1]) if not prices.empty else 0.0

    def _calculate_macd(self, prices: pd.Series) -> Dict[str, float]:
        """حساب MACD"""
        try:
            ema_12 = prices.ewm(span=12).mean()
            ema_26 = prices.ewm(span=26).mean()
            macd_line = ema_12 - ema_26
            signal_line = macd_line.ewm(span=9).mean()
            histogram = macd_line - signal_line

            return {
                'macd': float(macd_line.iloc[-1]) if not macd_line.empty else 0.0,
                'signal': float(signal_line.iloc[-1]) if not signal_line.empty else 0.0,
                'histogram': float(histogram.iloc[-1]) if not histogram.empty else 0.0
            }
        except Exception:
            return {'macd': 0.0, 'signal': 0.0, 'histogram': 0.0}

    def _calculate_bollinger_bands(self, prices: pd.Series, period: int = 20, std_dev: int = 2) -> Dict[str, float]:
        """حساب Bollinger Bands"""
        try:
            sma = prices.rolling(window=period).mean()
            std = prices.rolling(window=period).std()
            upper = sma + (std * std_dev)
            lower = sma - (std * std_dev)

            return {
                'upper': float(upper.iloc[-1]) if not upper.empty else 0.0,
                'middle': float(sma.iloc[-1]) if not sma.empty else 0.0,
                'lower': float(lower.iloc[-1]) if not lower.empty else 0.0
            }
        except Exception:
            return {'upper': 0.0, 'middle': 0.0, 'lower': 0.0}

    def _calculate_momentum(self, prices: pd.Series, period: int = 10) -> float:
        """حساب Momentum"""
        try:
            momentum = prices.diff(period)
            return float(momentum.iloc[-1]) if not momentum.empty else 0.0
        except Exception:
            return 0.0

    def _calculate_zscore(self, prices: pd.Series, period: int = 20) -> float:
        """حساب Z-Score"""
        try:
            rolling_mean = prices.rolling(window=period).mean()
            rolling_std = prices.rolling(window=period).std()
            zscore = (prices - rolling_mean) / rolling_std
            return float(zscore.iloc[-1]) if not zscore.empty else 0.0
        except Exception:
            return 0.0

    async def _update_performance_metrics(self):
        """تحديث مقاييس الأداء"""
        try:
            # حساب العوائد اليومية
            current_balance = self.trading_state['balance']
            daily_return = self.trading_state['daily_profit'] / current_balance if current_balance > 0 else 0

            self.performance_metrics['daily_returns'].append({
                'date': datetime.now().date().isoformat(),
                'return': daily_return,
                'profit': self.trading_state['daily_profit'],
                'trades': self.trading_state['daily_trades']
            })

            # الاحتفاظ بآخر 30 يوم فقط
            if len(self.performance_metrics['daily_returns']) > 30:
                self.performance_metrics['daily_returns'] = self.performance_metrics['daily_returns'][-30:]

            # حساب Sharpe Ratio
            if len(self.performance_metrics['daily_returns']) > 5:
                returns = [r['return'] for r in self.performance_metrics['daily_returns']]
                if np.std(returns) > 0:
                    self.performance_metrics['sharpe_ratio'] = np.mean(returns) / np.std(returns)

            # حساب أقصى انخفاض
            if self.performance_metrics['daily_returns']:
                cumulative_returns = np.cumsum([r['return'] for r in self.performance_metrics['daily_returns']])
                running_max = np.maximum.accumulate(cumulative_returns)
                drawdown = cumulative_returns - running_max
                self.performance_metrics['max_drawdown'] = float(np.min(drawdown))

        except Exception as e:
            logger.error(f"❌ خطأ في تحديث مقاييس الأداء: {e}")

    async def disconnect(self):
        """قطع الاتصال"""
        try:
            if self.trading_state['is_trading_active']:
                await self.stop_trading()

            await self.client.close()
            self.trading_state['is_connected'] = False
            logger.info("✅ تم قطع الاتصال بنجاح")

        except Exception as e:
            logger.error(f"❌ خطأ في قطع الاتصال: {e}")


# مثال على الاستخدام
async def main():
    """مثال على الاستخدام"""
    # إعداد التسجيل
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    # بيانات الحساب
    email = "<EMAIL>"
    password = "your_password"

    # إنشاء التكامل
    integration = AdvancedQuotexIntegration(email, password)

    try:
        # تهيئة الاتصال
        if await integration.initialize():
            # بدء التداول
            await integration.start_trading(['EURUSD', 'GBPUSD', 'USDJPY'])

    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف البرنامج بواسطة المستخدم")

    finally:
        # قطع الاتصال
        await integration.disconnect()


if __name__ == "__main__":
    asyncio.run(main())
