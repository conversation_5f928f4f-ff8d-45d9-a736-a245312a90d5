"""
نظام إدارة الحسابات المتقدم لـ PyQuotex
يدير التبديل بين الحسابات والرصيد وسجل الصفقات
"""

import os
import json
import time
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
import threading

logger = logging.getLogger(__name__)

class AccountManager:
    """مدير الحسابات الرئيسي للنظام"""
    
    def __init__(self, client):
        self.client = client
        self.data_dir = Path("data")
        self.accounts_dir = self.data_dir / "accounts"
        self.trades_dir = self.data_dir / "trades"
        
        # إنشاء المجلدات إذا لم تكن موجودة
        self._create_directories()
        
        # قفل للحماية من التداخل
        self.lock = threading.Lock()
        
        # معلومات الحساب الحالي
        self.current_account_type = None  # "demo" أو "real"
        self.current_balance = 0.0
        self.account_info = {}
        
    def _create_directories(self):
        """إنشاء المجلدات المطلوبة"""
        for directory in [self.data_dir, self.accounts_dir, self.trades_dir]:
            directory.mkdir(parents=True, exist_ok=True)
            
    async def switch_account(self, account_type: str) -> bool:
        """تبديل نوع الحساب (demo/real)"""
        try:
            if account_type not in ["demo", "real"]:
                logger.error(f"نوع حساب غير صحيح: {account_type}")
                return False
                
            # تحديد قيمة الحساب التجريبي
            is_demo = 1 if account_type == "demo" else 0
            
            # تبديل الحساب
            success = await self.client.change_account(is_demo)
            
            if success:
                self.current_account_type = account_type
                logger.info(f"✅ تم التبديل إلى الحساب {'التجريبي' if account_type == 'demo' else 'الحقيقي'}")
                
                # جلب معلومات الحساب الجديد
                await self._fetch_account_info()
                await self._fetch_account_balance()
                await self._fetch_trades_history()
                
                return True
            else:
                logger.error(f"فشل في التبديل إلى الحساب {account_type}")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في تبديل الحساب: {e}")
            return False
            
    async def _fetch_account_info(self):
        """جلب معلومات الحساب"""
        try:
            # جلب معلومات الملف الشخصي
            profile_data = await self.client.get_profile()
            
            if profile_data:
                self.account_info = {
                    'account_type': self.current_account_type,
                    'profile': profile_data,
                    'last_update': datetime.now().isoformat(),
                    'timestamp': time.time()
                }
                
                # حفظ معلومات الحساب
                await self._save_account_info()
                
                logger.info(f"✅ تم جلب معلومات الحساب {self.current_account_type}")
            else:
                logger.warning("لم يتم العثور على معلومات الملف الشخصي")
                
        except Exception as e:
            logger.error(f"خطأ في جلب معلومات الحساب: {e}")
            
    async def _fetch_account_balance(self):
        """جلب رصيد الحساب"""
        try:
            # جلب الرصيد
            balance = await self.client.get_balance()
            
            if balance is not None:
                self.current_balance = float(balance)
                
                # إضافة الرصيد لمعلومات الحساب
                if 'balance_history' not in self.account_info:
                    self.account_info['balance_history'] = []
                    
                self.account_info['balance_history'].append({
                    'balance': self.current_balance,
                    'timestamp': time.time(),
                    'datetime': datetime.now().isoformat()
                })
                
                # الاحتفاظ بآخر 100 سجل رصيد فقط
                if len(self.account_info['balance_history']) > 100:
                    self.account_info['balance_history'] = self.account_info['balance_history'][-100:]
                
                self.account_info['current_balance'] = self.current_balance
                
                logger.info(f"💰 رصيد الحساب {self.current_account_type}: {self.current_balance}")
            else:
                logger.warning("لم يتم العثور على رصيد الحساب")
                
        except Exception as e:
            logger.error(f"خطأ في جلب رصيد الحساب: {e}")
            
    async def _fetch_trades_history(self):
        """جلب سجل الصفقات"""
        try:
            # جلب سجل الصفقات
            trades_history = await self.client.get_history()
            
            if trades_history:
                # تنظيم بيانات الصفقات
                organized_trades = {
                    'account_type': self.current_account_type,
                    'total_trades': len(trades_history),
                    'last_update': datetime.now().isoformat(),
                    'timestamp': time.time(),
                    'trades': []
                }
                
                # إحصائيات الصفقات
                winning_trades = 0
                losing_trades = 0
                total_profit = 0.0
                
                for trade in trades_history:
                    trade_data = {
                        'id': trade.get('id'),
                        'asset': trade.get('asset'),
                        'amount': trade.get('amount'),
                        'direction': trade.get('direction'),
                        'result': trade.get('result'),
                        'profit': trade.get('profit', 0),
                        'open_time': trade.get('open_time'),
                        'close_time': trade.get('close_time'),
                        'status': trade.get('status')
                    }
                    
                    organized_trades['trades'].append(trade_data)
                    
                    # حساب الإحصائيات
                    if trade_data['result'] == 'win':
                        winning_trades += 1
                        total_profit += trade_data['profit']
                    elif trade_data['result'] == 'loss':
                        losing_trades += 1
                        total_profit += trade_data['profit']  # الخسارة ستكون قيمة سالبة
                
                # إضافة الإحصائيات
                organized_trades['statistics'] = {
                    'winning_trades': winning_trades,
                    'losing_trades': losing_trades,
                    'total_profit': total_profit,
                    'win_rate': (winning_trades / len(trades_history) * 100) if trades_history else 0
                }
                
                # حفظ سجل الصفقات
                await self._save_trades_history(organized_trades)
                
                logger.info(f"📊 تم جلب {len(trades_history)} صفقة للحساب {self.current_account_type}")
            else:
                logger.warning("لم يتم العثور على سجل صفقات")
                
        except Exception as e:
            logger.error(f"خطأ في جلب سجل الصفقات: {e}")
            
    async def _save_account_info(self):
        """حفظ معلومات الحساب"""
        try:
            file_path = self.accounts_dir / f"{self.current_account_type}_account_info.json"
            
            with self.lock:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.account_info, f, indent=2, ensure_ascii=False)
                    
                logger.debug(f"تم حفظ معلومات الحساب {self.current_account_type}")
                
        except Exception as e:
            logger.error(f"خطأ في حفظ معلومات الحساب: {e}")
            
    async def _save_trades_history(self, trades_data: Dict):
        """حفظ سجل الصفقات"""
        try:
            file_path = self.trades_dir / f"{self.current_account_type}_trades_history.json"
            
            with self.lock:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(trades_data, f, indent=2, ensure_ascii=False)
                    
                logger.debug(f"تم حفظ سجل الصفقات للحساب {self.current_account_type}")
                
        except Exception as e:
            logger.error(f"خطأ في حفظ سجل الصفقات: {e}")
            
    def load_account_info(self, account_type: str) -> Dict:
        """تحميل معلومات الحساب"""
        try:
            file_path = self.accounts_dir / f"{account_type}_account_info.json"
            
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
            
        except Exception as e:
            logger.error(f"خطأ في تحميل معلومات الحساب {account_type}: {e}")
            return {}
            
    def load_trades_history(self, account_type: str) -> Dict:
        """تحميل سجل الصفقات"""
        try:
            file_path = self.trades_dir / f"{account_type}_trades_history.json"
            
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
            
        except Exception as e:
            logger.error(f"خطأ في تحميل سجل الصفقات {account_type}: {e}")
            return {}
            
    def get_account_summary(self) -> Dict:
        """الحصول على ملخص الحساب الحالي"""
        return {
            'current_account_type': self.current_account_type,
            'current_balance': self.current_balance,
            'account_info': self.account_info,
            'demo_account_info': self.load_account_info('demo'),
            'real_account_info': self.load_account_info('real'),
            'demo_trades': self.load_trades_history('demo'),
            'real_trades': self.load_trades_history('real')
        }
        
    async def update_account_data(self):
        """تحديث بيانات الحساب الحالي"""
        if self.current_account_type:
            await self._fetch_account_balance()
            await self._fetch_trades_history()
