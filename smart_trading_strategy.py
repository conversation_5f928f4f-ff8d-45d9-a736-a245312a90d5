"""
استراتيجية التداول الذكية متعددة الطبقات للخيارات الثنائية
تجمع بين التحليل الفني والسلوكي والذكاء الاصطناعي
"""

import time
import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from enum import Enum
import json

logger = logging.getLogger(__name__)

class SignalType(Enum):
    """أنواع الإشارات"""
    CALL = "CALL"  # شراء
    PUT = "PUT"    # بيع
    HOLD = "HOLD"  # انتظار

class MarketCondition(Enum):
    """حالة السوق"""
    TRENDING = "trending"      # اتجاه واضح
    RANGING = "ranging"        # تذبذب جانبي
    VOLATILE = "volatile"      # متقلب
    STABLE = "stable"         # مستقر

class SmartTradingStrategy:
    """استراتيجية التداول الذكية"""
    
    def __init__(self):
        self.min_confidence = 80  # الحد الأدنى للثقة
        self.allowed_timeframes = [60, 120, 180, 300]  # 1-5 دقائق
        self.forbidden_hours = []  # ساعات منع التداول
        
        # إحصائيات الأداء
        self.performance_stats = {
            'total_signals': 0,
            'successful_signals': 0,
            'failed_signals': 0,
            'win_rate': 0.0
        }
        
    def analyze_market(self, candles: List[Dict], indicators: Dict) -> Dict[str, Any]:
        """تحليل شامل للسوق"""
        try:
            # 1. طبقة التحليل الفني
            technical_analysis = self._technical_analysis(candles, indicators)
            
            # 2. طبقة التحليل السلوكي
            behavioral_analysis = self._behavioral_analysis(candles)
            
            # 3. طبقة الذكاء الاصطناعي (مبسطة)
            ai_analysis = self._ai_analysis(candles, indicators)
            
            # 4. دمج النتائج
            final_decision = self._merge_analysis(technical_analysis, behavioral_analysis, ai_analysis)
            
            return final_decision
            
        except Exception as e:
            logger.error(f"خطأ في تحليل السوق: {e}")
            return self._get_hold_signal()
    
    def _technical_analysis(self, candles: List[Dict], indicators: Dict) -> Dict[str, Any]:
        """طبقة التحليل الفني"""
        try:
            signals = []
            confidence_scores = []
            
            # تحليل المتوسطات المتحركة
            if 'ema_5' in indicators and 'ema_10' in indicators:
                ema5 = indicators['ema_5']
                ema10 = indicators['ema_10']
                
                if ema5 > ema10:
                    signals.append(SignalType.CALL)
                    confidence_scores.append(70)
                elif ema5 < ema10:
                    signals.append(SignalType.PUT)
                    confidence_scores.append(70)
            
            # تحليل RSI
            if 'rsi_5' in indicators:
                rsi = indicators['rsi_5']
                if rsi < 30:  # منطقة تشبع بيعي
                    signals.append(SignalType.CALL)
                    confidence_scores.append(75)
                elif rsi > 70:  # منطقة تشبع شرائي
                    signals.append(SignalType.PUT)
                    confidence_scores.append(75)
            
            # تحليل Bollinger Bands
            if 'bollinger_bands' in indicators and len(candles) > 0:
                bb = indicators['bollinger_bands']
                current_price = candles[-1]['close']
                
                if current_price <= bb.get('lower', 0):
                    signals.append(SignalType.CALL)
                    confidence_scores.append(80)
                elif current_price >= bb.get('upper', float('inf')):
                    signals.append(SignalType.PUT)
                    confidence_scores.append(80)
            
            # تحليل MACD
            if 'macd' in indicators:
                macd_data = indicators['macd']
                macd_line = macd_data.get('macd', 0)
                signal_line = macd_data.get('signal', 0)
                
                if macd_line > signal_line and macd_line > 0:
                    signals.append(SignalType.CALL)
                    confidence_scores.append(65)
                elif macd_line < signal_line and macd_line < 0:
                    signals.append(SignalType.PUT)
                    confidence_scores.append(65)
            
            # حساب الإشارة الغالبة
            if signals:
                call_count = signals.count(SignalType.CALL)
                put_count = signals.count(SignalType.PUT)
                
                if call_count > put_count:
                    dominant_signal = SignalType.CALL
                elif put_count > call_count:
                    dominant_signal = SignalType.PUT
                else:
                    dominant_signal = SignalType.HOLD
                
                avg_confidence = np.mean(confidence_scores) if confidence_scores else 0
            else:
                dominant_signal = SignalType.HOLD
                avg_confidence = 0
            
            return {
                'signal': dominant_signal,
                'confidence': avg_confidence,
                'supporting_indicators': len(signals),
                'details': {
                    'signals': signals,
                    'confidence_scores': confidence_scores
                }
            }
            
        except Exception as e:
            logger.error(f"خطأ في التحليل الفني: {e}")
            return {'signal': SignalType.HOLD, 'confidence': 0}
    
    def _behavioral_analysis(self, candles: List[Dict]) -> Dict[str, Any]:
        """طبقة التحليل السلوكي للشموع"""
        try:
            if len(candles) < 3:
                return {'signal': SignalType.HOLD, 'confidence': 0}
            
            last_candles = candles[-3:]  # آخر 3 شموع
            signals = []
            confidence_scores = []
            
            # تحليل اتجاه الشموع
            trend_direction = self._analyze_candle_trend(last_candles)
            if trend_direction == 'bullish':
                signals.append(SignalType.CALL)
                confidence_scores.append(60)
            elif trend_direction == 'bearish':
                signals.append(SignalType.PUT)
                confidence_scores.append(60)
            
            # تحليل أنماط الشموع
            pattern = self._detect_candle_patterns(last_candles)
            if pattern:
                signals.append(pattern['signal'])
                confidence_scores.append(pattern['confidence'])
            
            # تحليل الزخم
            momentum = self._analyze_momentum(last_candles)
            if momentum:
                signals.append(momentum['signal'])
                confidence_scores.append(momentum['confidence'])
            
            # حساب النتيجة النهائية
            if signals:
                call_count = signals.count(SignalType.CALL)
                put_count = signals.count(SignalType.PUT)
                
                if call_count > put_count:
                    dominant_signal = SignalType.CALL
                elif put_count > call_count:
                    dominant_signal = SignalType.PUT
                else:
                    dominant_signal = SignalType.HOLD
                
                avg_confidence = np.mean(confidence_scores) if confidence_scores else 0
            else:
                dominant_signal = SignalType.HOLD
                avg_confidence = 0
            
            return {
                'signal': dominant_signal,
                'confidence': avg_confidence,
                'pattern_detected': pattern is not None,
                'trend_direction': trend_direction
            }
            
        except Exception as e:
            logger.error(f"خطأ في التحليل السلوكي: {e}")
            return {'signal': SignalType.HOLD, 'confidence': 0}
    
    def _ai_analysis(self, candles: List[Dict], indicators: Dict) -> Dict[str, Any]:
        """طبقة الذكاء الاصطناعي المبسطة"""
        try:
            # نموذج مبسط للذكاء الاصطناعي
            # في التطبيق الحقيقي، هنا سيكون نموذج ML مدرب
            
            features = self._extract_features(candles, indicators)
            
            # خوارزمية تقييم مبسطة
            score = self._simple_ml_model(features)
            
            if score > 0.6:
                signal = SignalType.CALL
                confidence = min(score * 100, 95)
            elif score < -0.6:
                signal = SignalType.PUT
                confidence = min(abs(score) * 100, 95)
            else:
                signal = SignalType.HOLD
                confidence = 0
            
            return {
                'signal': signal,
                'confidence': confidence,
                'ai_score': score,
                'features_count': len(features)
            }
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الذكاء الاصطناعي: {e}")
            return {'signal': SignalType.HOLD, 'confidence': 0}
    
    def _extract_features(self, candles: List[Dict], indicators: Dict) -> List[float]:
        """استخراج الميزات للذكاء الاصطناعي"""
        features = []
        
        try:
            # ميزات من الشموع
            if len(candles) >= 3:
                last_candles = candles[-3:]
                
                # اتجاه الأسعار
                price_trend = (last_candles[-1]['close'] - last_candles[0]['open']) / last_candles[0]['open']
                features.append(price_trend)
                
                # متوسط حجم الشموع
                avg_body_size = np.mean([abs(c['close'] - c['open']) / c['open'] for c in last_candles])
                features.append(avg_body_size)
            
            # ميزات من المؤشرات
            if 'rsi_5' in indicators:
                features.append((indicators['rsi_5'] - 50) / 50)  # تطبيع RSI
            
            if 'momentum_10' in indicators:
                features.append(np.tanh(indicators['momentum_10']))  # تطبيع Momentum
            
            if 'zscore_20' in indicators:
                features.append(np.tanh(indicators['zscore_20'] / 2))  # تطبيع Z-Score
            
        except Exception as e:
            logger.error(f"خطأ في استخراج الميزات: {e}")
        
        return features
    
    def _simple_ml_model(self, features: List[float]) -> float:
        """نموذج تعلم آلي مبسط"""
        if not features:
            return 0
        
        # أوزان مبسطة (في التطبيق الحقيقي ستكون مدربة)
        weights = [0.3, 0.2, 0.25, 0.15, 0.1][:len(features)]
        
        # حساب النتيجة المرجحة
        score = sum(f * w for f, w in zip(features, weights))
        
        # تطبيق دالة التفعيل
        return np.tanh(score)
    
    def _merge_analysis(self, technical: Dict, behavioral: Dict, ai: Dict) -> Dict[str, Any]:
        """دمج نتائج التحليل من جميع الطبقات"""
        try:
            # أوزان الطبقات
            weights = {
                'technical': 0.4,
                'behavioral': 0.3,
                'ai': 0.3
            }
            
            # جمع الإشارات
            signals = []
            confidences = []
            
            for analysis, weight in [
                (technical, weights['technical']),
                (behavioral, weights['behavioral']),
                (ai, weights['ai'])
            ]:
                if analysis['signal'] != SignalType.HOLD:
                    signals.append(analysis['signal'])
                    confidences.append(analysis['confidence'] * weight)
            
            if not signals:
                return self._get_hold_signal()
            
            # تحديد الإشارة الغالبة
            call_count = signals.count(SignalType.CALL)
            put_count = signals.count(SignalType.PUT)
            
            if call_count > put_count:
                final_signal = SignalType.CALL
            elif put_count > call_count:
                final_signal = SignalType.PUT
            else:
                return self._get_hold_signal()
            
            # حساب الثقة النهائية
            final_confidence = sum(confidences)
            
            # تحديد الإطار الزمني المناسب
            timeframe = self._determine_timeframe(technical, behavioral, ai)
            
            return {
                'signal': final_signal,
                'confidence': final_confidence,
                'timeframe': timeframe,
                'analysis_details': {
                    'technical': technical,
                    'behavioral': behavioral,
                    'ai': ai
                },
                'timestamp': time.time(),
                'datetime': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"خطأ في دمج التحليل: {e}")
            return self._get_hold_signal()
    
    def _determine_timeframe(self, technical: Dict, behavioral: Dict, ai: Dict) -> int:
        """تحديد الإطار الزمني المناسب للصفقة"""
        try:
            # حساب قوة الإشارة
            avg_confidence = np.mean([
                technical.get('confidence', 0),
                behavioral.get('confidence', 0),
                ai.get('confidence', 0)
            ])
            
            # تحديد الإطار الزمني بناءً على قوة الإشارة
            if avg_confidence >= 85:
                return 300  # 5 دقائق للإشارات القوية
            elif avg_confidence >= 75:
                return 180  # 3 دقائق للإشارات المتوسطة
            elif avg_confidence >= 65:
                return 120  # دقيقتان للإشارات الضعيفة
            else:
                return 60   # دقيقة واحدة للإشارات الأضعف
                
        except Exception:
            return 180  # افتراضي 3 دقائق
    
    def _get_hold_signal(self) -> Dict[str, Any]:
        """إرجاع إشارة انتظار"""
        return {
            'signal': SignalType.HOLD,
            'confidence': 0,
            'timeframe': 0,
            'reason': 'insufficient_confidence',
            'timestamp': time.time(),
            'datetime': datetime.now().isoformat()
        }

    def _analyze_candle_trend(self, candles: List[Dict]) -> str:
        """تحليل اتجاه الشموع"""
        try:
            if len(candles) < 2:
                return 'neutral'

            bullish_count = 0
            bearish_count = 0

            for candle in candles:
                if candle['close'] > candle['open']:
                    bullish_count += 1
                elif candle['close'] < candle['open']:
                    bearish_count += 1

            if bullish_count > bearish_count:
                return 'bullish'
            elif bearish_count > bullish_count:
                return 'bearish'
            else:
                return 'neutral'

        except Exception:
            return 'neutral'

    def _detect_candle_patterns(self, candles: List[Dict]) -> Optional[Dict]:
        """كشف أنماط الشموع"""
        try:
            if len(candles) < 2:
                return None

            last_candle = candles[-1]
            prev_candle = candles[-2]

            # نمط الابتلاع الصاعد
            if (prev_candle['close'] < prev_candle['open'] and  # شمعة هابطة سابقة
                last_candle['close'] > last_candle['open'] and   # شمعة صاعدة حالية
                last_candle['open'] < prev_candle['close'] and   # فتح أقل من إغلاق السابقة
                last_candle['close'] > prev_candle['open']):     # إغلاق أعلى من فتح السابقة

                return {
                    'signal': SignalType.CALL,
                    'confidence': 75,
                    'pattern': 'bullish_engulfing'
                }

            # نمط الابتلاع الهابط
            if (prev_candle['close'] > prev_candle['open'] and  # شمعة صاعدة سابقة
                last_candle['close'] < last_candle['open'] and   # شمعة هابطة حالية
                last_candle['open'] > prev_candle['close'] and   # فتح أعلى من إغلاق السابقة
                last_candle['close'] < prev_candle['open']):     # إغلاق أقل من فتح السابقة

                return {
                    'signal': SignalType.PUT,
                    'confidence': 75,
                    'pattern': 'bearish_engulfing'
                }

            # نمط الدوجي (عدم اليقين)
            body_size = abs(last_candle['close'] - last_candle['open'])
            candle_range = last_candle['high'] - last_candle['low']

            if candle_range > 0 and body_size / candle_range < 0.1:
                return {
                    'signal': SignalType.HOLD,
                    'confidence': 0,
                    'pattern': 'doji'
                }

            return None

        except Exception:
            return None

    def _analyze_momentum(self, candles: List[Dict]) -> Optional[Dict]:
        """تحليل الزخم"""
        try:
            if len(candles) < 3:
                return None

            # حساب متوسط حجم الشموع
            body_sizes = []
            for candle in candles:
                body_size = abs(candle['close'] - candle['open'])
                body_sizes.append(body_size)

            # إذا كانت الشمعة الأخيرة أكبر من المتوسط
            avg_body = np.mean(body_sizes[:-1])
            last_body = body_sizes[-1]

            if last_body > avg_body * 1.5:  # شمعة قوية
                last_candle = candles[-1]
                if last_candle['close'] > last_candle['open']:
                    return {
                        'signal': SignalType.CALL,
                        'confidence': 65,
                        'momentum': 'strong_bullish'
                    }
                else:
                    return {
                        'signal': SignalType.PUT,
                        'confidence': 65,
                        'momentum': 'strong_bearish'
                    }

            return None

        except Exception:
            return None

    def is_trading_allowed(self) -> bool:
        """التحقق من السماح بالتداول"""
        try:
            current_hour = datetime.now().hour

            # منع التداول في ساعات معينة
            if current_hour in self.forbidden_hours:
                return False

            # منع التداول في أول وآخر 5 دقائق من كل ساعة
            current_minute = datetime.now().minute
            if current_minute < 5 or current_minute > 55:
                return False

            return True

        except Exception:
            return False

    def update_performance(self, signal_result: bool):
        """تحديث إحصائيات الأداء"""
        try:
            self.performance_stats['total_signals'] += 1

            if signal_result:
                self.performance_stats['successful_signals'] += 1
            else:
                self.performance_stats['failed_signals'] += 1

            # حساب معدل النجاح
            total = self.performance_stats['total_signals']
            if total > 0:
                self.performance_stats['win_rate'] = (
                    self.performance_stats['successful_signals'] / total * 100
                )

        except Exception as e:
            logger.error(f"خطأ في تحديث الأداء: {e}")

    def get_performance_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأداء"""
        return self.performance_stats.copy()

    def should_trade(self, analysis_result: Dict) -> bool:
        """تحديد ما إذا كان يجب التداول"""
        try:
            # التحقق من السماح بالتداول
            if not self.is_trading_allowed():
                return False

            # التحقق من قوة الإشارة
            if analysis_result['signal'] == SignalType.HOLD:
                return False

            # التحقق من مستوى الثقة
            if analysis_result['confidence'] < self.min_confidence:
                return False

            return True

        except Exception:
            return False
