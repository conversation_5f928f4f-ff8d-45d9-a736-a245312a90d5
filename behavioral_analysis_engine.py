"""
محرك التحليل السلوكي المتقدم للسكالبينغ الاحترافي
يكشف أنماط الخوف والطمع وتحليل الشموع السلوكية
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class MarketSentiment(Enum):
    """معنويات السوق"""
    EXTREME_FEAR = -2
    FEAR = -1
    NEUTRAL = 0
    GREED = 1
    EXTREME_GREED = 2

class CandlePattern(Enum):
    """أنماط الشموع"""
    DOJI = "doji"
    HAMMER = "hammer"
    SHOOTING_STAR = "shooting_star"
    ENGULFING_BULLISH = "engulfing_bullish"
    ENGULFING_BEARISH = "engulfing_bearish"
    PIN_BAR_BULLISH = "pin_bar_bullish"
    PIN_BAR_BEARISH = "pin_bar_bearish"
    MARUBOZU_BULLISH = "marubozu_bullish"
    MARUBOZU_BEARISH = "marubozu_bearish"

@dataclass
class BehavioralSignal:
    """إشارة سلوكية"""
    pattern: str
    sentiment: MarketSentiment
    strength: float
    confidence: float
    psychological_factor: str
    details: Dict[str, Any]

class BehavioralAnalysisEngine:
    """محرك التحليل السلوكي المتقدم"""
    
    def __init__(self):
        # عتبات تحليل الشموع
        self.candle_thresholds = {
            'doji_body_ratio': 0.1,
            'hammer_wick_ratio': 2.0,
            'pin_bar_wick_ratio': 2.5,
            'marubozu_wick_ratio': 0.1,
            'engulfing_body_ratio': 1.2
        }
        
        # عوامل الخوف والطمع
        self.sentiment_factors = {
            'volume_spike_threshold': 1.5,
            'price_gap_threshold': 0.002,
            'volatility_spike_threshold': 1.8,
            'reversal_strength_threshold': 0.8
        }
        
        # أوزان العوامل النفسية
        self.psychological_weights = {
            'fear_selling': 0.3,
            'greed_buying': 0.3,
            'panic_reversal': 0.25,
            'euphoria_exhaustion': 0.15
        }
        
    def analyze_market(self, candles: List[Dict], indicators: Dict, 
                      volume_data: List[float] = None) -> Dict[str, Any]:
        """التحليل السلوكي الشامل للسوق"""
        try:
            if len(candles) < 10:
                return self._get_insufficient_data_result()
            
            # تحويل البيانات
            df = self._prepare_dataframe(candles, indicators, volume_data)
            
            # التحليلات السلوكية
            analyses = {}
            
            # 1. تحليل أنماط الشموع
            candle_patterns = self._analyze_candle_patterns(df)
            analyses['candle_patterns'] = candle_patterns
            
            # 2. تحليل معنويات السوق
            market_sentiment = self._analyze_market_sentiment(df)
            analyses['market_sentiment'] = market_sentiment
            
            # 3. تحليل سلوك الحجم
            volume_behavior = self._analyze_volume_behavior(df)
            analyses['volume_behavior'] = volume_behavior
            
            # 4. تحليل الخوف والطمع
            fear_greed_analysis = self._analyze_fear_greed_indicators(df)
            analyses['fear_greed'] = fear_greed_analysis
            
            # 5. تحليل السلوك الانعكاسي
            reversal_behavior = self._analyze_reversal_behavior(df)
            analyses['reversal_behavior'] = reversal_behavior
            
            # 6. تحليل الزخم النفسي
            psychological_momentum = self._analyze_psychological_momentum(df)
            analyses['psychological_momentum'] = psychological_momentum
            
            # دمج النتائج
            final_analysis = self._merge_behavioral_results(analyses, df)
            
            return final_analysis
            
        except Exception as e:
            logger.error(f"خطأ في التحليل السلوكي: {e}")
            return self._get_error_result()
    
    def _prepare_dataframe(self, candles: List[Dict], indicators: Dict, 
                          volume_data: List[float] = None) -> pd.DataFrame:
        """تحضير DataFrame للتحليل السلوكي"""
        try:
            df = pd.DataFrame(candles)
            
            # إضافة بيانات الحجم إذا توفرت
            if volume_data and len(volume_data) == len(df):
                df['volume'] = volume_data
            elif 'volume' not in df.columns:
                df['volume'] = 0
            
            # حساب خصائص الشموع
            df['body_size'] = abs(df['close'] - df['open'])
            df['upper_wick'] = df['high'] - df[['open', 'close']].max(axis=1)
            df['lower_wick'] = df[['open', 'close']].min(axis=1) - df['low']
            df['total_range'] = df['high'] - df['low']
            df['body_ratio'] = df['body_size'] / df['total_range']
            df['upper_wick_ratio'] = df['upper_wick'] / df['total_range']
            df['lower_wick_ratio'] = df['lower_wick'] / df['total_range']
            
            # حساب التغيرات
            df['price_change'] = df['close'].pct_change()
            df['volume_change'] = df['volume'].pct_change()
            df['volatility'] = df['price_change'].rolling(window=5).std()
            
            # إضافة المؤشرات
            for indicator, values in indicators.items():
                if isinstance(values, list) and len(values) > 0:
                    padded_values = [None] * (len(df) - len(values)) + values
                    df[indicator] = padded_values
            
            return df
            
        except Exception as e:
            logger.error(f"خطأ في تحضير البيانات السلوكية: {e}")
            return pd.DataFrame()
    
    def _analyze_candle_patterns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل أنماط الشموع السلوكية"""
        try:
            patterns_detected = []
            
            for i in range(1, len(df)):
                current_candle = df.iloc[i]
                prev_candle = df.iloc[i-1] if i > 0 else None
                
                # كشف أنماط الشموع المختلفة
                patterns = []
                
                # Doji
                if self._is_doji(current_candle):
                    patterns.append({
                        'pattern': CandlePattern.DOJI,
                        'strength': self._calculate_doji_strength(current_candle),
                        'psychological_meaning': 'indecision_uncertainty'
                    })
                
                # Hammer
                if self._is_hammer(current_candle):
                    patterns.append({
                        'pattern': CandlePattern.HAMMER,
                        'strength': self._calculate_hammer_strength(current_candle),
                        'psychological_meaning': 'rejection_of_lower_prices'
                    })
                
                # Shooting Star
                if self._is_shooting_star(current_candle):
                    patterns.append({
                        'pattern': CandlePattern.SHOOTING_STAR,
                        'strength': self._calculate_shooting_star_strength(current_candle),
                        'psychological_meaning': 'rejection_of_higher_prices'
                    })
                
                # Engulfing Patterns
                if prev_candle is not None:
                    engulfing = self._detect_engulfing_pattern(prev_candle, current_candle)
                    if engulfing:
                        patterns.append(engulfing)
                
                # Pin Bar
                pin_bar = self._detect_pin_bar(current_candle)
                if pin_bar:
                    patterns.append(pin_bar)
                
                # Marubozu
                marubozu = self._detect_marubozu(current_candle)
                if marubozu:
                    patterns.append(marubozu)
                
                if patterns:
                    patterns_detected.extend(patterns)
            
            # تحليل الأنماط المكتشفة
            pattern_analysis = self._analyze_detected_patterns(patterns_detected)
            
            return {
                'patterns_detected': patterns_detected,
                'pattern_analysis': pattern_analysis,
                'dominant_sentiment': self._determine_dominant_pattern_sentiment(patterns_detected),
                'pattern_reliability': self._assess_pattern_reliability(patterns_detected)
            }
            
        except Exception as e:
            logger.error(f"خطأ في تحليل أنماط الشموع: {e}")
            return {}
    
    def _analyze_market_sentiment(self, df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل معنويات السوق"""
        try:
            sentiment_indicators = []
            
            # تحليل اتجاه الأسعار
            price_sentiment = self._analyze_price_sentiment(df)
            sentiment_indicators.append(price_sentiment)
            
            # تحليل سلوك الحجم
            volume_sentiment = self._analyze_volume_sentiment(df)
            sentiment_indicators.append(volume_sentiment)
            
            # تحليل التقلبات
            volatility_sentiment = self._analyze_volatility_sentiment(df)
            sentiment_indicators.append(volatility_sentiment)
            
            # تحليل أنماط الانعكاس
            reversal_sentiment = self._analyze_reversal_sentiment(df)
            sentiment_indicators.append(reversal_sentiment)
            
            # دمج المعنويات
            overall_sentiment = self._merge_sentiment_indicators(sentiment_indicators)
            
            return {
                'individual_sentiments': sentiment_indicators,
                'overall_sentiment': overall_sentiment,
                'sentiment_strength': self._calculate_sentiment_strength(sentiment_indicators),
                'sentiment_consistency': self._assess_sentiment_consistency(sentiment_indicators)
            }
            
        except Exception as e:
            logger.error(f"خطأ في تحليل المعنويات: {e}")
            return {}
    
    def _analyze_fear_greed_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل مؤشرات الخوف والطمع"""
        try:
            fear_greed_signals = {}
            
            # مؤشرات الخوف
            fear_indicators = self._detect_fear_indicators(df)
            fear_greed_signals['fear_indicators'] = fear_indicators
            
            # مؤشرات الطمع
            greed_indicators = self._detect_greed_indicators(df)
            fear_greed_signals['greed_indicators'] = greed_indicators
            
            # تحليل الذعر
            panic_analysis = self._analyze_panic_behavior(df)
            fear_greed_signals['panic_analysis'] = panic_analysis
            
            # تحليل النشوة
            euphoria_analysis = self._analyze_euphoria_behavior(df)
            fear_greed_signals['euphoria_analysis'] = euphoria_analysis
            
            # حساب مؤشر الخوف والطمع الإجمالي
            fear_greed_index = self._calculate_fear_greed_index(fear_greed_signals)
            
            return {
                'fear_greed_signals': fear_greed_signals,
                'fear_greed_index': fear_greed_index,
                'dominant_emotion': self._determine_dominant_emotion(fear_greed_signals),
                'emotional_intensity': self._calculate_emotional_intensity(fear_greed_signals)
            }
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الخوف والطمع: {e}")
            return {}
    
    def _is_doji(self, candle: pd.Series) -> bool:
        """كشف نمط Doji"""
        try:
            body_ratio = candle['body_ratio']
            return body_ratio <= self.candle_thresholds['doji_body_ratio']
        except Exception:
            return False
    
    def _is_hammer(self, candle: pd.Series) -> bool:
        """كشف نمط Hammer"""
        try:
            lower_wick_ratio = candle['lower_wick_ratio']
            body_ratio = candle['body_ratio']
            upper_wick_ratio = candle['upper_wick_ratio']
            
            return (lower_wick_ratio >= 0.6 and 
                   body_ratio <= 0.3 and 
                   upper_wick_ratio <= 0.1)
        except Exception:
            return False
    
    def _is_shooting_star(self, candle: pd.Series) -> bool:
        """كشف نمط Shooting Star"""
        try:
            upper_wick_ratio = candle['upper_wick_ratio']
            body_ratio = candle['body_ratio']
            lower_wick_ratio = candle['lower_wick_ratio']
            
            return (upper_wick_ratio >= 0.6 and 
                   body_ratio <= 0.3 and 
                   lower_wick_ratio <= 0.1)
        except Exception:
            return False
    
    def _detect_engulfing_pattern(self, prev_candle: pd.Series, current_candle: pd.Series) -> Optional[Dict]:
        """كشف نمط الابتلاع"""
        try:
            prev_bullish = prev_candle['close'] > prev_candle['open']
            current_bullish = current_candle['close'] > current_candle['open']
            
            # ابتلاع صاعد
            if (not prev_bullish and current_bullish and
                current_candle['open'] < prev_candle['close'] and
                current_candle['close'] > prev_candle['open']):
                
                return {
                    'pattern': CandlePattern.ENGULFING_BULLISH,
                    'strength': self._calculate_engulfing_strength(prev_candle, current_candle),
                    'psychological_meaning': 'shift_from_selling_to_buying_pressure'
                }
            
            # ابتلاع هابط
            elif (prev_bullish and not current_bullish and
                  current_candle['open'] > prev_candle['close'] and
                  current_candle['close'] < prev_candle['open']):
                
                return {
                    'pattern': CandlePattern.ENGULFING_BEARISH,
                    'strength': self._calculate_engulfing_strength(prev_candle, current_candle),
                    'psychological_meaning': 'shift_from_buying_to_selling_pressure'
                }
            
            return None
            
        except Exception:
            return None

    def _detect_fear_indicators(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """كشف مؤشرات الخوف"""
        try:
            fear_signals = []

            # انخفاض حاد مع حجم عالي
            for i in range(1, len(df)):
                current = df.iloc[i]
                prev = df.iloc[i-1]

                price_drop = (current['close'] - prev['close']) / prev['close']
                volume_spike = current['volume'] / prev['volume'] if prev['volume'] > 0 else 1

                if (price_drop < -0.01 and
                    volume_spike > self.sentiment_factors['volume_spike_threshold']):

                    fear_signals.append({
                        'type': 'panic_selling',
                        'intensity': abs(price_drop) * volume_spike,
                        'timestamp': i,
                        'details': {
                            'price_drop': price_drop,
                            'volume_spike': volume_spike
                        }
                    })

                # شموع طويلة هابطة مع ذيول سفلية طويلة
                if (current['close'] < current['open'] and
                    current['body_ratio'] > 0.6 and
                    current['lower_wick_ratio'] > 0.2):

                    fear_signals.append({
                        'type': 'fear_rejection',
                        'intensity': current['body_ratio'] + current['lower_wick_ratio'],
                        'timestamp': i,
                        'details': {
                            'body_ratio': current['body_ratio'],
                            'lower_wick_ratio': current['lower_wick_ratio']
                        }
                    })

            return fear_signals

        except Exception as e:
            logger.error(f"خطأ في كشف مؤشرات الخوف: {e}")
            return []

    def _detect_greed_indicators(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """كشف مؤشرات الطمع"""
        try:
            greed_signals = []

            # ارتفاع حاد مع حجم عالي
            for i in range(1, len(df)):
                current = df.iloc[i]
                prev = df.iloc[i-1]

                price_rise = (current['close'] - prev['close']) / prev['close']
                volume_spike = current['volume'] / prev['volume'] if prev['volume'] > 0 else 1

                if (price_rise > 0.01 and
                    volume_spike > self.sentiment_factors['volume_spike_threshold']):

                    greed_signals.append({
                        'type': 'greed_buying',
                        'intensity': price_rise * volume_spike,
                        'timestamp': i,
                        'details': {
                            'price_rise': price_rise,
                            'volume_spike': volume_spike
                        }
                    })

                # شموع طويلة صاعدة مع ذيول علوية طويلة
                if (current['close'] > current['open'] and
                    current['body_ratio'] > 0.6 and
                    current['upper_wick_ratio'] > 0.2):

                    greed_signals.append({
                        'type': 'greed_exhaustion',
                        'intensity': current['body_ratio'] + current['upper_wick_ratio'],
                        'timestamp': i,
                        'details': {
                            'body_ratio': current['body_ratio'],
                            'upper_wick_ratio': current['upper_wick_ratio']
                        }
                    })

            return greed_signals

        except Exception as e:
            logger.error(f"خطأ في كشف مؤشرات الطمع: {e}")
            return []

    def _analyze_volume_behavior(self, df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل سلوك الحجم"""
        try:
            if 'volume' not in df.columns or df['volume'].sum() == 0:
                return {'no_volume_data': True}

            volume_analysis = {}

            # تحليل ارتفاعات الحجم
            volume_spikes = self._detect_volume_spikes(df)
            volume_analysis['volume_spikes'] = volume_spikes

            # تحليل علاقة الحجم بالسعر
            price_volume_relationship = self._analyze_price_volume_relationship(df)
            volume_analysis['price_volume_relationship'] = price_volume_relationship

            # تحليل توزيع الحجم
            volume_distribution = self._analyze_volume_distribution(df)
            volume_analysis['volume_distribution'] = volume_distribution

            # تحليل الحجم النسبي
            relative_volume = self._calculate_relative_volume(df)
            volume_analysis['relative_volume'] = relative_volume

            return volume_analysis

        except Exception as e:
            logger.error(f"خطأ في تحليل سلوك الحجم: {e}")
            return {}

    def _merge_behavioral_results(self, analyses: Dict[str, Any], df: pd.DataFrame) -> Dict[str, Any]:
        """دمج نتائج التحليل السلوكي"""
        try:
            # حساب النتيجة السلوكية الإجمالية
            behavioral_score = self._calculate_behavioral_score(analyses)

            # تحديد المعنويات الغالبة
            dominant_sentiment = self._determine_overall_sentiment(analyses)

            # تحليل قوة الإشارة السلوكية
            signal_strength = self._assess_behavioral_signal_strength(analyses)

            # تحليل الاتساق السلوكي
            behavioral_consistency = self._assess_behavioral_consistency(analyses)

            # توصيات سلوكية
            behavioral_recommendations = self._generate_behavioral_recommendations(analyses)

            # تحليل المخاطر السلوكية
            behavioral_risks = self._assess_behavioral_risks(analyses)

            return {
                'signal_type': 'BEHAVIORAL_ANALYSIS',
                'behavioral_score': behavioral_score,
                'dominant_sentiment': dominant_sentiment.name if isinstance(dominant_sentiment, MarketSentiment) else dominant_sentiment,
                'signal_strength': signal_strength,
                'behavioral_consistency': behavioral_consistency,
                'recommendations': behavioral_recommendations,
                'risks': behavioral_risks,
                'detailed_analyses': analyses,
                'confidence': self._calculate_behavioral_confidence(analyses),
                'timestamp': pd.Timestamp.now().isoformat()
            }

        except Exception as e:
            logger.error(f"خطأ في دمج النتائج السلوكية: {e}")
            return self._get_error_result()

    def _calculate_behavioral_score(self, analyses: Dict[str, Any]) -> float:
        """حساب النتيجة السلوكية الإجمالية"""
        try:
            scores = []
            weights = {
                'candle_patterns': 0.3,
                'market_sentiment': 0.25,
                'fear_greed': 0.25,
                'volume_behavior': 0.2
            }

            # نتيجة أنماط الشموع
            if 'candle_patterns' in analyses:
                pattern_reliability = analyses['candle_patterns'].get('pattern_reliability', {}).get('score', 0.5)
                scores.append(pattern_reliability * weights['candle_patterns'])

            # نتيجة المعنويات
            if 'market_sentiment' in analyses:
                sentiment_strength = analyses['market_sentiment'].get('sentiment_strength', 0.5)
                scores.append(sentiment_strength * weights['market_sentiment'])

            # نتيجة الخوف والطمع
            if 'fear_greed' in analyses:
                emotional_intensity = analyses['fear_greed'].get('emotional_intensity', 0.5)
                scores.append(emotional_intensity * weights['fear_greed'])

            # نتيجة سلوك الحجم
            if 'volume_behavior' in analyses and 'no_volume_data' not in analyses['volume_behavior']:
                volume_score = self._calculate_volume_behavior_score(analyses['volume_behavior'])
                scores.append(volume_score * weights['volume_behavior'])

            return sum(scores) if scores else 0.0

        except Exception:
            return 0.0

    def _determine_overall_sentiment(self, analyses: Dict[str, Any]) -> MarketSentiment:
        """تحديد المعنويات الإجمالية"""
        try:
            sentiment_votes = []

            # تصويت أنماط الشموع
            if 'candle_patterns' in analyses:
                pattern_sentiment = analyses['candle_patterns'].get('dominant_sentiment')
                if pattern_sentiment:
                    sentiment_votes.append(pattern_sentiment)

            # تصويت معنويات السوق
            if 'market_sentiment' in analyses:
                market_sentiment = analyses['market_sentiment'].get('overall_sentiment')
                if market_sentiment:
                    sentiment_votes.append(market_sentiment)

            # تصويت الخوف والطمع
            if 'fear_greed' in analyses:
                dominant_emotion = analyses['fear_greed'].get('dominant_emotion')
                if dominant_emotion:
                    sentiment_votes.append(dominant_emotion)

            # تحديد المعنويات الغالبة
            if not sentiment_votes:
                return MarketSentiment.NEUTRAL

            # حساب المتوسط المرجح
            sentiment_values = []
            for sentiment in sentiment_votes:
                if isinstance(sentiment, str):
                    sentiment_values.append(self._sentiment_string_to_value(sentiment))
                elif isinstance(sentiment, MarketSentiment):
                    sentiment_values.append(sentiment.value)

            avg_sentiment = np.mean(sentiment_values) if sentiment_values else 0

            return self._value_to_sentiment(avg_sentiment)

        except Exception:
            return MarketSentiment.NEUTRAL

    def _sentiment_string_to_value(self, sentiment_str: str) -> int:
        """تحويل نص المعنويات إلى قيمة رقمية"""
        sentiment_map = {
            'extreme_fear': -2,
            'fear': -1,
            'neutral': 0,
            'greed': 1,
            'extreme_greed': 2,
            'bearish': -1,
            'bullish': 1,
            'panic': -2,
            'euphoria': 2
        }
        return sentiment_map.get(sentiment_str.lower(), 0)

    def _value_to_sentiment(self, value: float) -> MarketSentiment:
        """تحويل القيمة الرقمية إلى معنويات"""
        if value <= -1.5:
            return MarketSentiment.EXTREME_FEAR
        elif value <= -0.5:
            return MarketSentiment.FEAR
        elif value >= 1.5:
            return MarketSentiment.EXTREME_GREED
        elif value >= 0.5:
            return MarketSentiment.GREED
        else:
            return MarketSentiment.NEUTRAL

    def _calculate_behavioral_confidence(self, analyses: Dict[str, Any]) -> float:
        """حساب الثقة السلوكية"""
        try:
            confidence_factors = []

            # ثقة أنماط الشموع
            if 'candle_patterns' in analyses:
                pattern_reliability = analyses['candle_patterns'].get('pattern_reliability', {}).get('confidence', 0.5)
                confidence_factors.append(pattern_reliability)

            # ثقة المعنويات
            if 'market_sentiment' in analyses:
                sentiment_consistency = analyses['market_sentiment'].get('sentiment_consistency', 0.5)
                confidence_factors.append(sentiment_consistency)

            # ثقة الخوف والطمع
            if 'fear_greed' in analyses:
                emotional_clarity = analyses['fear_greed'].get('emotional_intensity', 0.5)
                confidence_factors.append(emotional_clarity)

            return np.mean(confidence_factors) if confidence_factors else 0.0

        except Exception:
            return 0.0

    def _get_insufficient_data_result(self) -> Dict[str, Any]:
        """نتيجة عدم كفاية البيانات"""
        return {
            'signal_type': 'BEHAVIORAL_ANALYSIS',
            'behavioral_score': 0,
            'dominant_sentiment': 'NEUTRAL',
            'signal_strength': 'insufficient_data',
            'confidence': 0,
            'reason': 'insufficient_data_for_behavioral_analysis',
            'timestamp': pd.Timestamp.now().isoformat()
        }

    def _get_error_result(self) -> Dict[str, Any]:
        """نتيجة الخطأ"""
        return {
            'signal_type': 'BEHAVIORAL_ANALYSIS',
            'behavioral_score': 0,
            'dominant_sentiment': 'NEUTRAL',
            'signal_strength': 'error',
            'confidence': 0,
            'reason': 'behavioral_analysis_error',
            'timestamp': pd.Timestamp.now().isoformat()
        }
