"""
محرك التحليل الفني المتقدم للسكالبينغ الاحترافي
يجمع إشارات متعددة مع أوزان ديناميكية وفلترة ذكية
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class SignalStrength(Enum):
    """قوة الإشارة"""
    VERY_WEAK = 1
    WEAK = 2
    MODERATE = 3
    STRONG = 4
    VERY_STRONG = 5

class TrendDirection(Enum):
    """اتجاه الترند"""
    BULLISH = 1
    BEARISH = -1
    SIDEWAYS = 0

@dataclass
class TechnicalSignal:
    """إشارة فنية"""
    indicator: str
    direction: TrendDirection
    strength: SignalStrength
    confidence: float
    weight: float
    details: Dict[str, Any]

class TechnicalAnalysisEngine:
    """محرك التحليل الفني المتقدم"""
    
    def __init__(self):
        # أوزان المؤشرات (قابلة للتعديل ديناميكياً)
        self.indicator_weights = {
            'ema_crossover': 0.25,
            'rsi_divergence': 0.20,
            'bollinger_squeeze': 0.15,
            'macd_momentum': 0.15,
            'volume_confirmation': 0.10,
            'support_resistance': 0.15
        }
        
        # عتبات الإشارات
        self.signal_thresholds = {
            'rsi_oversold': 30,
            'rsi_overbought': 70,
            'bb_squeeze_threshold': 0.1,
            'volume_spike_multiplier': 1.5,
            'trend_strength_min': 0.6
        }
        
        # تاريخ أداء المؤشرات (للتعديل الديناميكي)
        self.performance_history = {}
        
    def analyze_market(self, candles: List[Dict], indicators: Dict) -> Dict[str, Any]:
        """التحليل الفني الشامل للسوق"""
        try:
            if len(candles) < 30:
                return self._get_insufficient_data_result()
            
            # تحويل البيانات إلى DataFrame للمعالجة
            df = self._prepare_dataframe(candles, indicators)
            
            # تحليل كل مؤشر
            signals = []
            
            # 1. تحليل تقاطع المتوسطات المتحركة
            ema_signal = self._analyze_ema_crossover(df)
            if ema_signal:
                signals.append(ema_signal)
            
            # 2. تحليل RSI مع الانحرافات
            rsi_signal = self._analyze_rsi_divergence(df)
            if rsi_signal:
                signals.append(rsi_signal)
            
            # 3. تحليل Bollinger Bands Squeeze
            bb_signal = self._analyze_bollinger_squeeze(df)
            if bb_signal:
                signals.append(bb_signal)
            
            # 4. تحليل MACD Momentum
            macd_signal = self._analyze_macd_momentum(df)
            if macd_signal:
                signals.append(macd_signal)
            
            # 5. تحليل تأكيد الحجم
            volume_signal = self._analyze_volume_confirmation(df)
            if volume_signal:
                signals.append(volume_signal)
            
            # 6. تحليل الدعم والمقاومة
            sr_signal = self._analyze_support_resistance(df)
            if sr_signal:
                signals.append(sr_signal)
            
            # دمج الإشارات وحساب النتيجة النهائية
            final_analysis = self._merge_signals(signals, df)
            
            return final_analysis
            
        except Exception as e:
            logger.error(f"خطأ في التحليل الفني: {e}")
            return self._get_error_result()
    
    def _prepare_dataframe(self, candles: List[Dict], indicators: Dict) -> pd.DataFrame:
        """تحضير DataFrame للتحليل"""
        try:
            # تحويل الشموع إلى DataFrame
            df = pd.DataFrame(candles)
            
            # إضافة المؤشرات
            for indicator, values in indicators.items():
                if isinstance(values, list) and len(values) > 0:
                    # ملء القيم المفقودة في بداية السلسلة
                    padded_values = [None] * (len(df) - len(values)) + values
                    df[indicator] = padded_values
                elif isinstance(values, dict):
                    # للمؤشرات المركبة مثل MACD, Bollinger Bands
                    for key, value_list in values.items():
                        if isinstance(value_list, list) and len(value_list) > 0:
                            padded_values = [None] * (len(df) - len(value_list)) + value_list
                            df[f"{indicator}_{key}"] = padded_values
            
            # حساب مؤشرات إضافية
            df['price_change'] = df['close'].pct_change()
            df['volatility'] = df['price_change'].rolling(window=10).std()
            df['volume_ma'] = df.get('volume', pd.Series([0]*len(df))).rolling(window=10).mean()
            
            return df
            
        except Exception as e:
            logger.error(f"خطأ في تحضير البيانات: {e}")
            return pd.DataFrame()
    
    def _analyze_ema_crossover(self, df: pd.DataFrame) -> Optional[TechnicalSignal]:
        """تحليل تقاطع المتوسطات المتحركة الأسية"""
        try:
            if 'ema_5' not in df.columns or 'ema_10' not in df.columns:
                return None
            
            # الحصول على آخر القيم
            ema5_current = df['ema_5'].iloc[-1]
            ema5_prev = df['ema_5'].iloc[-2]
            ema10_current = df['ema_10'].iloc[-1]
            ema10_prev = df['ema_10'].iloc[-2]
            
            if pd.isna(ema5_current) or pd.isna(ema10_current):
                return None
            
            # كشف التقاطع
            crossover_bullish = (ema5_prev <= ema10_prev) and (ema5_current > ema10_current)
            crossover_bearish = (ema5_prev >= ema10_prev) and (ema5_current < ema10_current)
            
            if crossover_bullish:
                # حساب قوة الإشارة بناءً على المسافة بين المتوسطات
                distance = abs(ema5_current - ema10_current) / ema10_current
                strength = self._calculate_signal_strength(distance, [0.001, 0.003, 0.005, 0.01])
                
                return TechnicalSignal(
                    indicator='ema_crossover',
                    direction=TrendDirection.BULLISH,
                    strength=strength,
                    confidence=min(0.8 + distance * 100, 0.95),
                    weight=self.indicator_weights['ema_crossover'],
                    details={
                        'ema5': ema5_current,
                        'ema10': ema10_current,
                        'distance': distance,
                        'type': 'golden_cross'
                    }
                )
            
            elif crossover_bearish:
                distance = abs(ema5_current - ema10_current) / ema10_current
                strength = self._calculate_signal_strength(distance, [0.001, 0.003, 0.005, 0.01])
                
                return TechnicalSignal(
                    indicator='ema_crossover',
                    direction=TrendDirection.BEARISH,
                    strength=strength,
                    confidence=min(0.8 + distance * 100, 0.95),
                    weight=self.indicator_weights['ema_crossover'],
                    details={
                        'ema5': ema5_current,
                        'ema10': ema10_current,
                        'distance': distance,
                        'type': 'death_cross'
                    }
                )
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في تحليل EMA: {e}")
            return None
    
    def _analyze_rsi_divergence(self, df: pd.DataFrame) -> Optional[TechnicalSignal]:
        """تحليل RSI مع كشف الانحرافات"""
        try:
            if 'rsi_5' not in df.columns:
                return None
            
            rsi_current = df['rsi_5'].iloc[-1]
            rsi_prev = df['rsi_5'].iloc[-2]
            price_current = df['close'].iloc[-1]
            price_prev = df['close'].iloc[-2]
            
            if pd.isna(rsi_current) or pd.isna(rsi_prev):
                return None
            
            # كشف مناطق التشبع
            if rsi_current <= self.signal_thresholds['rsi_oversold']:
                # منطقة تشبع بيعي - إشارة شراء محتملة
                strength = self._calculate_rsi_strength(rsi_current, 'oversold')
                
                # كشف الانحراف الإيجابي
                divergence_strength = self._detect_bullish_divergence(df, window=5)
                
                return TechnicalSignal(
                    indicator='rsi_divergence',
                    direction=TrendDirection.BULLISH,
                    strength=strength,
                    confidence=0.7 + divergence_strength * 0.2,
                    weight=self.indicator_weights['rsi_divergence'],
                    details={
                        'rsi_value': rsi_current,
                        'divergence_strength': divergence_strength,
                        'zone': 'oversold'
                    }
                )
            
            elif rsi_current >= self.signal_thresholds['rsi_overbought']:
                # منطقة تشبع شرائي - إشارة بيع محتملة
                strength = self._calculate_rsi_strength(rsi_current, 'overbought')
                
                # كشف الانحراف السلبي
                divergence_strength = self._detect_bearish_divergence(df, window=5)
                
                return TechnicalSignal(
                    indicator='rsi_divergence',
                    direction=TrendDirection.BEARISH,
                    strength=strength,
                    confidence=0.7 + divergence_strength * 0.2,
                    weight=self.indicator_weights['rsi_divergence'],
                    details={
                        'rsi_value': rsi_current,
                        'divergence_strength': divergence_strength,
                        'zone': 'overbought'
                    }
                )
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في تحليل RSI: {e}")
            return None
    
    def _analyze_bollinger_squeeze(self, df: pd.DataFrame) -> Optional[TechnicalSignal]:
        """تحليل انضغاط Bollinger Bands"""
        try:
            if 'bollinger_bands_upper' not in df.columns or 'bollinger_bands_lower' not in df.columns:
                return None
            
            # حساب عرض النطاق
            bb_width = (df['bollinger_bands_upper'] - df['bollinger_bands_lower']) / df['bollinger_bands_middle']
            current_width = bb_width.iloc[-1]
            avg_width = bb_width.rolling(window=20).mean().iloc[-1]
            
            if pd.isna(current_width) or pd.isna(avg_width):
                return None
            
            # كشف الانضغاط
            squeeze_ratio = current_width / avg_width
            
            if squeeze_ratio < self.signal_thresholds['bb_squeeze_threshold']:
                # انضغاط قوي - توقع انفجار قريب
                price_position = self._calculate_bb_position(df)
                
                # تحديد الاتجاه المحتمل بناءً على موقع السعر
                if price_position > 0.6:
                    direction = TrendDirection.BULLISH
                elif price_position < 0.4:
                    direction = TrendDirection.BEARISH
                else:
                    direction = TrendDirection.SIDEWAYS
                
                strength = self._calculate_signal_strength(1 - squeeze_ratio, [0.5, 0.7, 0.8, 0.9])
                
                return TechnicalSignal(
                    indicator='bollinger_squeeze',
                    direction=direction,
                    strength=strength,
                    confidence=0.75 + (1 - squeeze_ratio) * 0.2,
                    weight=self.indicator_weights['bollinger_squeeze'],
                    details={
                        'squeeze_ratio': squeeze_ratio,
                        'price_position': price_position,
                        'width_current': current_width,
                        'width_average': avg_width
                    }
                )
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في تحليل Bollinger Bands: {e}")
            return None
    
    def _calculate_signal_strength(self, value: float, thresholds: List[float]) -> SignalStrength:
        """حساب قوة الإشارة بناءً على العتبات"""
        if value <= thresholds[0]:
            return SignalStrength.VERY_WEAK
        elif value <= thresholds[1]:
            return SignalStrength.WEAK
        elif value <= thresholds[2]:
            return SignalStrength.MODERATE
        elif value <= thresholds[3]:
            return SignalStrength.STRONG
        else:
            return SignalStrength.VERY_STRONG
    
    def _calculate_rsi_strength(self, rsi_value: float, zone: str) -> SignalStrength:
        """حساب قوة إشارة RSI"""
        if zone == 'oversold':
            if rsi_value <= 20:
                return SignalStrength.VERY_STRONG
            elif rsi_value <= 25:
                return SignalStrength.STRONG
            else:
                return SignalStrength.MODERATE
        else:  # overbought
            if rsi_value >= 80:
                return SignalStrength.VERY_STRONG
            elif rsi_value >= 75:
                return SignalStrength.STRONG
            else:
                return SignalStrength.MODERATE

    def _detect_bullish_divergence(self, df: pd.DataFrame, window: int = 5) -> float:
        """كشف الانحراف الإيجابي"""
        try:
            if len(df) < window * 2:
                return 0.0

            # مقارنة آخر قيعان في السعر و RSI
            price_lows = df['close'].rolling(window=window).min()
            rsi_lows = df['rsi_5'].rolling(window=window).min()

            recent_price_low = price_lows.iloc[-1]
            prev_price_low = price_lows.iloc[-window]
            recent_rsi_low = rsi_lows.iloc[-1]
            prev_rsi_low = rsi_lows.iloc[-window]

            # انحراف إيجابي: السعر يصنع قاع أدنى بينما RSI يصنع قاع أعلى
            if (recent_price_low < prev_price_low) and (recent_rsi_low > prev_rsi_low):
                divergence_strength = (recent_rsi_low - prev_rsi_low) / prev_rsi_low
                return min(divergence_strength, 1.0)

            return 0.0

        except Exception:
            return 0.0

    def _detect_bearish_divergence(self, df: pd.DataFrame, window: int = 5) -> float:
        """كشف الانحراف السلبي"""
        try:
            if len(df) < window * 2:
                return 0.0

            # مقارنة آخر قمم في السعر و RSI
            price_highs = df['close'].rolling(window=window).max()
            rsi_highs = df['rsi_5'].rolling(window=window).max()

            recent_price_high = price_highs.iloc[-1]
            prev_price_high = price_highs.iloc[-window]
            recent_rsi_high = rsi_highs.iloc[-1]
            prev_rsi_high = rsi_highs.iloc[-window]

            # انحراف سلبي: السعر يصنع قمة أعلى بينما RSI يصنع قمة أدنى
            if (recent_price_high > prev_price_high) and (recent_rsi_high < prev_rsi_high):
                divergence_strength = (prev_rsi_high - recent_rsi_high) / prev_rsi_high
                return min(divergence_strength, 1.0)

            return 0.0

        except Exception:
            return 0.0

    def _calculate_bb_position(self, df: pd.DataFrame) -> float:
        """حساب موقع السعر داخل نطاق Bollinger Bands"""
        try:
            current_price = df['close'].iloc[-1]
            bb_upper = df['bollinger_bands_upper'].iloc[-1]
            bb_lower = df['bollinger_bands_lower'].iloc[-1]

            if pd.isna(bb_upper) or pd.isna(bb_lower):
                return 0.5

            # موقع السعر كنسبة مئوية (0 = أسفل النطاق، 1 = أعلى النطاق)
            position = (current_price - bb_lower) / (bb_upper - bb_lower)
            return max(0, min(1, position))

        except Exception:
            return 0.5

    def _analyze_macd_momentum(self, df: pd.DataFrame) -> Optional[TechnicalSignal]:
        """تحليل زخم MACD"""
        try:
            if 'macd_macd' not in df.columns or 'macd_signal' not in df.columns:
                return None

            macd_line = df['macd_macd'].iloc[-1]
            signal_line = df['macd_signal'].iloc[-1]
            macd_prev = df['macd_macd'].iloc[-2]
            signal_prev = df['macd_signal'].iloc[-2]

            if pd.isna(macd_line) or pd.isna(signal_line):
                return None

            # كشف تقاطع MACD
            bullish_cross = (macd_prev <= signal_prev) and (macd_line > signal_line)
            bearish_cross = (macd_prev >= signal_prev) and (macd_line < signal_line)

            if bullish_cross and macd_line > 0:
                # تقاطع صاعد فوق الصفر - إشارة قوية
                momentum_strength = abs(macd_line - signal_line)
                strength = self._calculate_signal_strength(momentum_strength, [0.0001, 0.0005, 0.001, 0.002])

                return TechnicalSignal(
                    indicator='macd_momentum',
                    direction=TrendDirection.BULLISH,
                    strength=strength,
                    confidence=0.8,
                    weight=self.indicator_weights['macd_momentum'],
                    details={
                        'macd': macd_line,
                        'signal': signal_line,
                        'momentum': momentum_strength,
                        'cross_type': 'bullish_above_zero'
                    }
                )

            elif bearish_cross and macd_line < 0:
                # تقاطع هابط تحت الصفر - إشارة قوية
                momentum_strength = abs(macd_line - signal_line)
                strength = self._calculate_signal_strength(momentum_strength, [0.0001, 0.0005, 0.001, 0.002])

                return TechnicalSignal(
                    indicator='macd_momentum',
                    direction=TrendDirection.BEARISH,
                    strength=strength,
                    confidence=0.8,
                    weight=self.indicator_weights['macd_momentum'],
                    details={
                        'macd': macd_line,
                        'signal': signal_line,
                        'momentum': momentum_strength,
                        'cross_type': 'bearish_below_zero'
                    }
                )

            return None

        except Exception as e:
            logger.error(f"خطأ في تحليل MACD: {e}")
            return None

    def _analyze_volume_confirmation(self, df: pd.DataFrame) -> Optional[TechnicalSignal]:
        """تحليل تأكيد الحجم"""
        try:
            if 'volume' not in df.columns or df['volume'].isna().all():
                return None

            current_volume = df['volume'].iloc[-1]
            avg_volume = df['volume_ma'].iloc[-1]
            price_change = df['price_change'].iloc[-1]

            if pd.isna(current_volume) or pd.isna(avg_volume) or avg_volume == 0:
                return None

            volume_ratio = current_volume / avg_volume

            # كشف ارتفاع الحجم مع حركة السعر
            if volume_ratio > self.signal_thresholds['volume_spike_multiplier']:
                if price_change > 0:
                    # حجم عالي مع ارتفاع السعر
                    strength = self._calculate_signal_strength(volume_ratio, [1.5, 2.0, 3.0, 5.0])

                    return TechnicalSignal(
                        indicator='volume_confirmation',
                        direction=TrendDirection.BULLISH,
                        strength=strength,
                        confidence=0.7 + min(volume_ratio * 0.1, 0.2),
                        weight=self.indicator_weights['volume_confirmation'],
                        details={
                            'volume_ratio': volume_ratio,
                            'price_change': price_change,
                            'confirmation_type': 'bullish_volume_spike'
                        }
                    )

                elif price_change < 0:
                    # حجم عالي مع انخفاض السعر
                    strength = self._calculate_signal_strength(volume_ratio, [1.5, 2.0, 3.0, 5.0])

                    return TechnicalSignal(
                        indicator='volume_confirmation',
                        direction=TrendDirection.BEARISH,
                        strength=strength,
                        confidence=0.7 + min(volume_ratio * 0.1, 0.2),
                        weight=self.indicator_weights['volume_confirmation'],
                        details={
                            'volume_ratio': volume_ratio,
                            'price_change': price_change,
                            'confirmation_type': 'bearish_volume_spike'
                        }
                    )

            return None

        except Exception as e:
            logger.error(f"خطأ في تحليل الحجم: {e}")
            return None

    def _analyze_support_resistance(self, df: pd.DataFrame) -> Optional[TechnicalSignal]:
        """تحليل مستويات الدعم والمقاومة"""
        try:
            if len(df) < 20:
                return None

            current_price = df['close'].iloc[-1]

            # حساب مستويات الدعم والمقاومة من آخر 20 شمعة
            recent_highs = df['high'].tail(20)
            recent_lows = df['low'].tail(20)

            # العثور على مستويات مهمة
            resistance_levels = self._find_resistance_levels(recent_highs)
            support_levels = self._find_support_levels(recent_lows)

            # التحقق من القرب من مستويات مهمة
            nearest_resistance = self._find_nearest_level(current_price, resistance_levels, 'above')
            nearest_support = self._find_nearest_level(current_price, support_levels, 'below')

            # تحليل الإشارات
            if nearest_support and self._is_near_level(current_price, nearest_support, 0.001):
                # قريب من الدعم - إشارة شراء محتملة
                strength = self._calculate_support_resistance_strength(current_price, nearest_support)

                return TechnicalSignal(
                    indicator='support_resistance',
                    direction=TrendDirection.BULLISH,
                    strength=strength,
                    confidence=0.75,
                    weight=self.indicator_weights['support_resistance'],
                    details={
                        'level': nearest_support,
                        'distance': abs(current_price - nearest_support) / current_price,
                        'type': 'support_bounce'
                    }
                )

            elif nearest_resistance and self._is_near_level(current_price, nearest_resistance, 0.001):
                # قريب من المقاومة - إشارة بيع محتملة
                strength = self._calculate_support_resistance_strength(current_price, nearest_resistance)

                return TechnicalSignal(
                    indicator='support_resistance',
                    direction=TrendDirection.BEARISH,
                    strength=strength,
                    confidence=0.75,
                    weight=self.indicator_weights['support_resistance'],
                    details={
                        'level': nearest_resistance,
                        'distance': abs(current_price - nearest_resistance) / current_price,
                        'type': 'resistance_rejection'
                    }
                )

            return None

        except Exception as e:
            logger.error(f"خطأ في تحليل الدعم والمقاومة: {e}")
            return None

    def _find_resistance_levels(self, highs: pd.Series) -> List[float]:
        """العثور على مستويات المقاومة"""
        try:
            # البحث عن القمم المحلية
            levels = []
            for i in range(2, len(highs) - 2):
                if (highs.iloc[i] > highs.iloc[i-1] and highs.iloc[i] > highs.iloc[i-2] and
                    highs.iloc[i] > highs.iloc[i+1] and highs.iloc[i] > highs.iloc[i+2]):
                    levels.append(highs.iloc[i])

            # إزالة المستويات المتقاربة
            return self._consolidate_levels(levels)

        except Exception:
            return []

    def _find_support_levels(self, lows: pd.Series) -> List[float]:
        """العثور على مستويات الدعم"""
        try:
            # البحث عن القيعان المحلية
            levels = []
            for i in range(2, len(lows) - 2):
                if (lows.iloc[i] < lows.iloc[i-1] and lows.iloc[i] < lows.iloc[i-2] and
                    lows.iloc[i] < lows.iloc[i+1] and lows.iloc[i] < lows.iloc[i+2]):
                    levels.append(lows.iloc[i])

            # إزالة المستويات المتقاربة
            return self._consolidate_levels(levels)

        except Exception:
            return []

    def _consolidate_levels(self, levels: List[float], threshold: float = 0.001) -> List[float]:
        """دمج المستويات المتقاربة"""
        if not levels:
            return []

        consolidated = []
        sorted_levels = sorted(levels)

        current_group = [sorted_levels[0]]

        for level in sorted_levels[1:]:
            if abs(level - current_group[-1]) / current_group[-1] <= threshold:
                current_group.append(level)
            else:
                # حساب متوسط المجموعة الحالية
                consolidated.append(sum(current_group) / len(current_group))
                current_group = [level]

        # إضافة آخر مجموعة
        consolidated.append(sum(current_group) / len(current_group))

        return consolidated

    def _find_nearest_level(self, price: float, levels: List[float], direction: str) -> Optional[float]:
        """العثور على أقرب مستوى"""
        if not levels:
            return None

        if direction == 'above':
            # البحث عن أقرب مستوى أعلى من السعر
            above_levels = [level for level in levels if level > price]
            return min(above_levels) if above_levels else None
        else:
            # البحث عن أقرب مستوى أسفل من السعر
            below_levels = [level for level in levels if level < price]
            return max(below_levels) if below_levels else None

    def _is_near_level(self, price: float, level: float, threshold: float) -> bool:
        """التحقق من القرب من مستوى معين"""
        return abs(price - level) / price <= threshold

    def _calculate_support_resistance_strength(self, price: float, level: float) -> SignalStrength:
        """حساب قوة إشارة الدعم/المقاومة"""
        distance_ratio = abs(price - level) / price

        if distance_ratio <= 0.0005:
            return SignalStrength.VERY_STRONG
        elif distance_ratio <= 0.001:
            return SignalStrength.STRONG
        elif distance_ratio <= 0.002:
            return SignalStrength.MODERATE
        else:
            return SignalStrength.WEAK

    def _merge_signals(self, signals: List[TechnicalSignal], df: pd.DataFrame) -> Dict[str, Any]:
        """دمج جميع الإشارات الفنية"""
        try:
            if not signals:
                return self._get_no_signal_result()

            # تجميع الإشارات حسب الاتجاه
            bullish_signals = [s for s in signals if s.direction == TrendDirection.BULLISH]
            bearish_signals = [s for s in signals if s.direction == TrendDirection.BEARISH]

            # حساب النتائج المرجحة
            bullish_score = self._calculate_weighted_score(bullish_signals)
            bearish_score = self._calculate_weighted_score(bearish_signals)

            # تحديد الاتجاه النهائي
            if bullish_score > bearish_score and bullish_score > 0.6:
                final_direction = TrendDirection.BULLISH
                final_confidence = min(bullish_score * 100, 95)
            elif bearish_score > bullish_score and bearish_score > 0.6:
                final_direction = TrendDirection.BEARISH
                final_confidence = min(bearish_score * 100, 95)
            else:
                return self._get_no_signal_result()

            # حساب قوة الإشارة الإجمالية
            total_strength = self._calculate_total_strength(signals, final_direction)

            # تحديد الإطار الزمني المناسب
            timeframe = self._determine_optimal_timeframe(signals, final_confidence)

            # تحليل جودة الإشارة
            signal_quality = self._assess_signal_quality(signals, df)

            return {
                'signal_type': 'TECHNICAL_ANALYSIS',
                'direction': final_direction.name,
                'confidence': final_confidence,
                'strength': total_strength.name,
                'timeframe': timeframe,
                'quality_score': signal_quality,
                'supporting_indicators': len(signals),
                'bullish_score': bullish_score,
                'bearish_score': bearish_score,
                'signals_breakdown': {
                    'bullish': [self._signal_to_dict(s) for s in bullish_signals],
                    'bearish': [self._signal_to_dict(s) for s in bearish_signals]
                },
                'market_context': self._analyze_market_context(df),
                'timestamp': pd.Timestamp.now().isoformat()
            }

        except Exception as e:
            logger.error(f"خطأ في دمج الإشارات: {e}")
            return self._get_error_result()

    def _calculate_weighted_score(self, signals: List[TechnicalSignal]) -> float:
        """حساب النتيجة المرجحة للإشارات"""
        if not signals:
            return 0.0

        total_score = 0.0
        total_weight = 0.0

        for signal in signals:
            # تحويل قوة الإشارة إلى رقم
            strength_multiplier = signal.strength.value / 5.0

            # حساب نتيجة الإشارة
            signal_score = signal.confidence * strength_multiplier * signal.weight

            total_score += signal_score
            total_weight += signal.weight

        return total_score / total_weight if total_weight > 0 else 0.0

    def _calculate_total_strength(self, signals: List[TechnicalSignal], direction: TrendDirection) -> SignalStrength:
        """حساب القوة الإجمالية للإشارة"""
        relevant_signals = [s for s in signals if s.direction == direction]

        if not relevant_signals:
            return SignalStrength.VERY_WEAK

        # حساب متوسط القوة
        avg_strength = sum(s.strength.value for s in relevant_signals) / len(relevant_signals)

        if avg_strength >= 4.5:
            return SignalStrength.VERY_STRONG
        elif avg_strength >= 3.5:
            return SignalStrength.STRONG
        elif avg_strength >= 2.5:
            return SignalStrength.MODERATE
        elif avg_strength >= 1.5:
            return SignalStrength.WEAK
        else:
            return SignalStrength.VERY_WEAK

    def _determine_optimal_timeframe(self, signals: List[TechnicalSignal], confidence: float) -> int:
        """تحديد الإطار الزمني الأمثل"""
        # تحديد الإطار الزمني بناءً على قوة الإشارة والثقة
        if confidence >= 90:
            return 300  # 5 دقائق للإشارات القوية جداً
        elif confidence >= 85:
            return 240  # 4 دقائق للإشارات القوية
        elif confidence >= 80:
            return 180  # 3 دقائق للإشارات المتوسطة القوية
        elif confidence >= 75:
            return 120  # دقيقتان للإشارات المتوسطة
        else:
            return 60   # دقيقة واحدة للإشارات الضعيفة

    def _assess_signal_quality(self, signals: List[TechnicalSignal], df: pd.DataFrame) -> float:
        """تقييم جودة الإشارة"""
        try:
            quality_factors = []

            # عامل التنوع: تنوع المؤشرات
            unique_indicators = len(set(s.indicator for s in signals))
            diversity_factor = min(unique_indicators / 4, 1.0)  # أقصى 4 مؤشرات مختلفة
            quality_factors.append(diversity_factor)

            # عامل الاتساق: اتفاق الإشارات
            if signals:
                directions = [s.direction for s in signals]
                most_common_direction = max(set(directions), key=directions.count)
                consistency_factor = directions.count(most_common_direction) / len(directions)
                quality_factors.append(consistency_factor)

            # عامل التقلبات: استقرار السوق
            if 'volatility' in df.columns and not df['volatility'].isna().all():
                current_volatility = df['volatility'].iloc[-1]
                avg_volatility = df['volatility'].mean()

                if pd.notna(current_volatility) and pd.notna(avg_volatility) and avg_volatility > 0:
                    volatility_factor = 1 - min(current_volatility / avg_volatility, 2.0) / 2.0
                    quality_factors.append(volatility_factor)

            return sum(quality_factors) / len(quality_factors) if quality_factors else 0.5

        except Exception:
            return 0.5

    def _analyze_market_context(self, df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل سياق السوق"""
        try:
            context = {}

            # اتجاه السوق العام
            if len(df) >= 10:
                recent_prices = df['close'].tail(10)
                price_trend = (recent_prices.iloc[-1] - recent_prices.iloc[0]) / recent_prices.iloc[0]

                if price_trend > 0.002:
                    context['market_trend'] = 'strong_uptrend'
                elif price_trend > 0.0005:
                    context['market_trend'] = 'uptrend'
                elif price_trend < -0.002:
                    context['market_trend'] = 'strong_downtrend'
                elif price_trend < -0.0005:
                    context['market_trend'] = 'downtrend'
                else:
                    context['market_trend'] = 'sideways'

            # مستوى التقلبات
            if 'volatility' in df.columns and not df['volatility'].isna().all():
                current_vol = df['volatility'].iloc[-1]
                avg_vol = df['volatility'].mean()

                if pd.notna(current_vol) and pd.notna(avg_vol) and avg_vol > 0:
                    vol_ratio = current_vol / avg_vol
                    if vol_ratio > 1.5:
                        context['volatility_level'] = 'high'
                    elif vol_ratio > 1.2:
                        context['volatility_level'] = 'elevated'
                    elif vol_ratio < 0.8:
                        context['volatility_level'] = 'low'
                    else:
                        context['volatility_level'] = 'normal'

            return context

        except Exception:
            return {'market_trend': 'unknown', 'volatility_level': 'unknown'}

    def _signal_to_dict(self, signal: TechnicalSignal) -> Dict[str, Any]:
        """تحويل الإشارة إلى قاموس"""
        return {
            'indicator': signal.indicator,
            'direction': signal.direction.name,
            'strength': signal.strength.name,
            'confidence': signal.confidence,
            'weight': signal.weight,
            'details': signal.details
        }

    def _get_insufficient_data_result(self) -> Dict[str, Any]:
        """نتيجة عدم كفاية البيانات"""
        return {
            'signal_type': 'TECHNICAL_ANALYSIS',
            'direction': 'HOLD',
            'confidence': 0,
            'reason': 'insufficient_data',
            'timestamp': pd.Timestamp.now().isoformat()
        }

    def _get_no_signal_result(self) -> Dict[str, Any]:
        """نتيجة عدم وجود إشارة"""
        return {
            'signal_type': 'TECHNICAL_ANALYSIS',
            'direction': 'HOLD',
            'confidence': 0,
            'reason': 'no_clear_signal',
            'timestamp': pd.Timestamp.now().isoformat()
        }

    def _get_error_result(self) -> Dict[str, Any]:
        """نتيجة الخطأ"""
        return {
            'signal_type': 'TECHNICAL_ANALYSIS',
            'direction': 'HOLD',
            'confidence': 0,
            'reason': 'analysis_error',
            'timestamp': pd.Timestamp.now().isoformat()
        }
