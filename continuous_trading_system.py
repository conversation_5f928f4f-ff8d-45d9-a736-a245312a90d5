"""
نظام التداول المستمر لـ PyQuotex
يعمل 24/7 مع إعادة الاتصال التلقائي وجلب البيانات المباشرة
"""

import asyncio
import logging
import time
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any
from concurrent.futures import ThreadPoolExecutor
import threading

from pyquotex.stable_api import Quotex
from pyquotex.config import credentials
from pyquotex.utils.indicators import TechnicalIndicators
from data_manager import DataManager
from account_manager import AccountManager

logger = logging.getLogger(__name__)

class ContinuousTradingSystem:
    """نظام التداول المستمر"""
    
    def __init__(self):
        self.client: Optional[Quotex] = None
        self.data_manager = DataManager()
        self.indicators = TechnicalIndicators()
        self.account_manager: Optional[AccountManager] = None
        
        # حالة النظام
        self.is_running = False
        self.is_connected = False
        self.connection_attempts = 0
        self.max_connection_attempts = 10
        
        # إعدادات البيانات
        self.timeframe = 300  # 5 دقائق
        self.historical_candles_count = 500
        
        # قوائم البيانات
        self.traditional_assets = {}
        self.active_subscriptions = set()
        
        # خيوط العمل
        self.executor = ThreadPoolExecutor(max_workers=10)
        self.tasks = []
        
        # إعداد العميل
        self._setup_client()
        
    def _setup_client(self):
        """إعداد عميل Quotex"""
        try:
            email, password = credentials()
            self.client = Quotex(
                email=email,
                password=password,
                lang="pt"
            )
            logger.info("تم إعداد عميل Quotex بنجاح")
        except Exception as e:
            logger.error(f"فشل في إعداد عميل Quotex: {e}")
            raise
            
    async def start_system(self):
        """بدء النظام المستمر"""
        logger.info("🚀 بدء نظام التداول المستمر...")
        self.is_running = True
        
        try:
            # الاتصال بالمنصة
            await self._connect_with_retry()
            
            if not self.is_connected:
                logger.error("فشل في الاتصال بالمنصة")
                return
                
            # جلب بيانات الملف الشخصي
            await self._fetch_profile_data()
            
            # جلب قائمة الأزواج التقليدية
            await self._fetch_traditional_assets()
            
            # جلب البيانات التاريخية
            await self._fetch_historical_data()
            
            # بدء الاشتراك في البيانات المباشرة
            await self._start_realtime_subscriptions()
            
            # بدء حلقة المراقبة الرئيسية
            await self._main_monitoring_loop()
            
        except Exception as e:
            logger.error(f"خطأ في النظام: {e}")
        finally:
            await self.stop_system()
            
    async def stop_system(self):
        """إيقاف النظام"""
        logger.info("⏹️ إيقاف نظام التداول المستمر...")
        self.is_running = False
        
        # إلغاء جميع المهام
        for task in self.tasks:
            if not task.done():
                task.cancel()
                
        # إغلاق الاتصال
        if self.client and self.is_connected:
            try:
                await self.client.close()
                logger.info("تم إغلاق الاتصال بنجاح")
            except Exception as e:
                logger.error(f"خطأ في إغلاق الاتصال: {e}")
                
        # إغلاق ThreadPoolExecutor
        self.executor.shutdown(wait=True)
        
    async def _connect_with_retry(self):
        """الاتصال مع إعادة المحاولة"""
        self.connection_attempts = 0
        
        while self.connection_attempts < self.max_connection_attempts and self.is_running:
            try:
                logger.info(f"محاولة الاتصال #{self.connection_attempts + 1}")
                
                check, reason = await self.client.connect()
                
                if check:
                    self.is_connected = True
                    self.connection_attempts = 0

                    # تهيئة مدير الحسابات
                    self.account_manager = AccountManager(self.client)

                    logger.info(f"✅ تم الاتصال بنجاح: {reason}")
                    return True
                else:
                    self.connection_attempts += 1
                    logger.warning(f"❌ فشل الاتصال: {reason}")
                    
                    if self.connection_attempts < self.max_connection_attempts:
                        wait_time = min(30, 5 * self.connection_attempts)
                        logger.info(f"انتظار {wait_time} ثانية قبل المحاولة التالية...")
                        await asyncio.sleep(wait_time)
                        
            except Exception as e:
                self.connection_attempts += 1
                logger.error(f"خطأ في الاتصال: {e}")
                
                if self.connection_attempts < self.max_connection_attempts:
                    await asyncio.sleep(10)
                    
        logger.error("فشل في الاتصال بعد جميع المحاولات")
        return False
        
    async def _fetch_profile_data(self):
        """جلب بيانات الملف الشخصي والحسابات"""
        try:
            logger.info("📊 جلب بيانات الملف الشخصي...")

            # انتظار حتى يتم تحميل البيانات
            await asyncio.sleep(2)

            # جلب بيانات الملف الشخصي
            profile = await self.client.get_profile()

            if profile is None:
                logger.warning("لم يتم تحميل بيانات الملف الشخصي بعد، سيتم المحاولة لاحقاً")
                return

            # جلب أرصدة الحسابات
            self.client.change_account("PRACTICE")
            await asyncio.sleep(1)
            demo_balance = await self.client.get_balance()

            self.client.change_account("REAL")
            await asyncio.sleep(1)
            live_balance = await self.client.get_balance()

            # تجميع البيانات
            profile_data = {
                'profile_id': profile.profile_id,
                'nick_name': profile.nick_name,
                'avatar': profile.avatar,
                'country_name': profile.country_name,
                'offset': profile.offset,
                'demo_balance': demo_balance,
                'live_balance': live_balance,
                'last_update': datetime.now().isoformat()
            }

            # حفظ البيانات
            self.data_manager.save_profile_data(profile_data)

            logger.info(f"✅ تم جلب بيانات الملف الشخصي - الحساب التجريبي: {demo_balance}, الحساب الحقيقي: {live_balance}")

        except Exception as e:
            logger.error(f"خطأ في جلب بيانات الملف الشخصي: {e}")
            
    async def _fetch_traditional_assets(self):
        """جلب قائمة الأزواج التقليدية مع نسب الربح"""
        try:
            logger.info("💱 جلب قائمة الأزواج التقليدية...")

            # انتظار حتى يتم تحميل الأدوات
            await asyncio.sleep(2)

            # جلب جميع الأصول المتاحة
            all_assets = self.client.get_all_asset_name()

            if not all_assets:
                logger.warning("لم يتم تحميل قائمة الأصول بعد، سيتم المحاولة لاحقاً")
                return

            traditional_assets = {}

            for asset_info in all_assets:
                if len(asset_info) < 2:
                    continue
                asset_name = asset_info[0]  # اسم الأصل
                try:
                    # التحقق من أن الأصل تقليدي (فوركس فقط)
                    if not self._is_traditional_asset(asset_name):
                        continue

                    # جلب معلومات الأصل
                    asset_info_detail = await self.client.get_available_asset(asset_name)

                    if asset_info_detail and len(asset_info_detail) >= 2:
                        asset_data = asset_info_detail[1]

                        # جلب نسبة الربح
                        payout = self.client.get_payout_by_asset(asset_name)

                        # التحقق من نوع البيانات
                        is_open = False
                        if isinstance(asset_data, (list, tuple)) and len(asset_data) >= 3:
                            is_open = asset_data[2] if asset_data[2] is not None else False
                        elif isinstance(asset_data, dict):
                            is_open = asset_data.get('open', False)

                        traditional_assets[asset_name] = {
                            'name': asset_name,
                            'is_open': is_open,
                            'payout': payout if payout is not None else 0,
                            'last_update': datetime.now().isoformat()
                        }
                            
                except Exception as e:
                    logger.warning(f"خطأ في جلب بيانات الأصل {asset_name}: {e}")
                    continue
                    
            # حفظ الأزواج التقليدية
            self.traditional_assets = traditional_assets
            self.data_manager.save_traditional_assets(traditional_assets)
            
            logger.info(f"✅ تم جلب {len(traditional_assets)} زوج تقليدي")
            
        except Exception as e:
            logger.error(f"خطأ في جلب الأزواج التقليدية: {e}")
            
    def _is_traditional_asset(self, asset_name: str) -> bool:
        """التحقق من أن الأصل تقليدي (فوركس فقط)"""
        # قائمة العملات التقليدية المدعومة
        traditional_currencies = [
            'EUR', 'USD', 'GBP', 'JPY', 'CHF', 'CAD', 'AUD', 'NZD',
            'SEK', 'NOK', 'DKK', 'PLN', 'CZK', 'HUF', 'TRY', 'ZAR'
        ]

        # استبعاد العملات المشفرة بشكل شامل
        crypto_keywords = [
            'BTC', 'ETH', 'LTC', 'XRP', 'ADA', 'DOT', 'LINK', 'UNI', 'DOGE', 'SHIB',
            'APT', 'ARB', 'ATO', 'AVA', 'AXS', 'BCH', 'BEA', 'BNB', 'BON', 'DAS',
            'DOG', 'ETC', 'FLO', 'GAL', 'HMS', 'LIN', 'MAN', 'MEL', 'NOT', 'PEP',
            'SHI', 'SOL', 'TIA', 'TON', 'TRU', 'TRX', 'WIF', 'ZEC'
        ]

        # استبعاد الأسهم والمؤشرات
        stock_keywords = [
            'AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN', 'META', 'NVDA',
            'DJI', 'NDX', 'F40', 'FTS', 'IBX', 'JPX', 'STX'
        ]

        # استبعاد المعادن
        metal_keywords = ['XAU', 'XAG', 'GOLD', 'SILVER']

        # استبعاد العملات الغريبة
        exotic_keywords = [
            'ARS', 'BDT', 'COP', 'DZD', 'EGP', 'IDR', 'INR', 'MXN',
            'NGN', 'PHP', 'PKR', 'BRL', 'SGD'
        ]

        asset_upper = asset_name.upper()

        # التحقق من العملات المشفرة
        for crypto in crypto_keywords:
            if crypto in asset_upper:
                return False

        # التحقق من الأسهم
        for stock in stock_keywords:
            if stock in asset_upper:
                return False

        # التحقق من المعادن
        for metal in metal_keywords:
            if metal in asset_upper:
                return False

        # التحقق من العملات الغريبة
        for exotic in exotic_keywords:
            if exotic in asset_upper:
                return False

        # التحقق من كون الأصل زوج عملات تقليدي
        clean_asset = asset_upper.replace('_OTC', '')

        # يجب أن يكون طوله 6 أحرف (مثل EURUSD)
        if len(clean_asset) == 6:
            base_currency = clean_asset[:3]
            quote_currency = clean_asset[3:]

            return (base_currency in traditional_currencies and
                    quote_currency in traditional_currencies)

        return False
        
    async def _fetch_historical_data(self):
        """جلب البيانات التاريخية لجميع الأزواج"""
        try:
            logger.info("📈 بدء جلب البيانات التاريخية...")

            # تحميل قائمة الأزواج التقليدية
            traditional_assets = self.data_manager.load_traditional_assets()

            if not traditional_assets:
                logger.warning("لا توجد أزواج تقليدية محفوظة")
                return

            logger.info(f"📊 سيتم جلب البيانات التاريخية لـ {len(traditional_assets)} زوج...")

            # فلترة الأزواج التقليدية فقط (فوركس)
            forex_assets = {}
            for asset_name, asset_data in traditional_assets.items():
                if self._is_traditional_asset(asset_name):
                    forex_assets[asset_name] = asset_data

            logger.info(f"📊 سيتم جلب البيانات التاريخية لـ {len(forex_assets)} زوج فوركس من أصل {len(traditional_assets)} زوج...")

            # جلب البيانات التاريخية لكل زوج فوركس (تسلسلي لتجنب الضغط على الخادم)
            processed = 0
            for asset_name in forex_assets.keys():
                processed += 1
                logger.info(f"📈 [{processed}/{len(forex_assets)}] جلب البيانات التاريخية لـ {asset_name}...")
                await self._fetch_asset_historical_data(asset_name)

                # انتظار قصير لتجنب الضغط على الخادم
                await asyncio.sleep(0.2)

            logger.info(f"✅ انتهى جلب البيانات التاريخية لـ {processed} زوج فوركس")

        except Exception as e:
            logger.error(f"خطأ في جلب البيانات التاريخية: {e}")
        
    async def _fetch_asset_historical_data(self, asset_name: str):
        """جلب البيانات التاريخية لأصل واحد"""
        try:
            # التحقق من آخر شمعة محفوظة
            last_timestamp = self.data_manager.get_last_candle_timestamp(asset_name, self.timeframe)
            
            if last_timestamp:
                # جلب البيانات من آخر شمعة
                end_time = time.time()
                start_time = last_timestamp + self.timeframe  # البدء من الشمعة التالية
                
                if start_time >= end_time:
                    logger.info(f"البيانات التاريخية لـ {asset_name} محدثة")
                    return
                    
                # حساب عدد الشموع المطلوبة
                time_diff = end_time - start_time
                candles_needed = min(int(time_diff / self.timeframe), 100)  # حد أقصى 100 شمعة في المرة
                
            else:
                # جلب 500 شمعة كاملة
                candles_needed = self.historical_candles_count
                end_time = time.time()
                
            # جلب البيانات من المنصة
            candles = await self.client.get_candles(
                asset_name,
                end_time,
                candles_needed * self.timeframe,
                self.timeframe
            )

            if candles:
                # معالجة البيانات إذا لزم الأمر
                if not candles[0].get("open"):
                    from pyquotex.utils.processor import process_candles
                    candles = process_candles(candles, self.timeframe)

                # إصلاح التوقيت وإضافة المؤشرات للشموع
                candles_with_indicators = await self._process_candles_with_indicators(asset_name, candles)

                if last_timestamp:
                    # تحديث البيانات الموجودة
                    self.data_manager.update_historical_candles(asset_name, candles_with_indicators, self.timeframe)
                else:
                    # حفظ البيانات الجديدة
                    self.data_manager.save_historical_candles(asset_name, candles_with_indicators, self.timeframe)

                logger.info(f"✅ تم جلب {len(candles_with_indicators)} شمعة تاريخية مع المؤشرات لـ {asset_name}")
                
            else:
                logger.warning(f"لم يتم العثور على بيانات تاريخية لـ {asset_name}")
                
        except Exception as e:
            logger.error(f"خطأ في جلب البيانات التاريخية لـ {asset_name}: {e}")

    async def _process_candles_with_indicators(self, asset_name: str, candles: List[Dict]) -> List[Dict]:
        """معالجة الشموع مع إضافة المؤشرات وإصلاح التوقيت"""
        try:
            if len(candles) < 20:  # نحتاج على الأقل 20 شمعة للمؤشرات
                return candles

            # إصلاح التوقيت أولاً
            fixed_candles = []
            current_time = int(time.time())

            # تقريب الوقت الحالي لأقرب شمعة 5 دقائق
            current_candle_time = (current_time // self.timeframe) * self.timeframe

            for i, candle in enumerate(candles):
                fixed_candle = candle.copy()

                # حساب التوقيت الصحيح للشمعة
                # آخر شمعة تكون الأحدث، والشموع السابقة تكون أقدم
                candle_time = current_candle_time - ((len(candles) - 1 - i) * self.timeframe)

                fixed_candle['time'] = candle_time
                fixed_candle['timestamp'] = candle_time

                # إضافة تنسيق مقروء للوقت
                readable_time = datetime.fromtimestamp(candle_time).strftime('%Y-%m-%d %H:%M:%S')
                fixed_candle['datetime'] = readable_time

                fixed_candles.append(fixed_candle)

            # استخراج البيانات للمؤشرات
            closes = [float(candle.get('close', 0)) for candle in fixed_candles]
            highs = [float(candle.get('high', 0)) for candle in fixed_candles]
            lows = [float(candle.get('low', 0)) for candle in fixed_candles]
            opens = [float(candle.get('open', 0)) for candle in fixed_candles]

            # حساب المؤشرات المحسنة
            indicators_data = {}

            # RSI متعدد الفترات (5, 14)
            rsi_periods = [5, 14]
            rsi_data = self.indicators.calculate_multiple_rsi(closes, rsi_periods)
            indicators_data.update(rsi_data)

            # EMA متعدد الفترات (5, 10, 21)
            ema_periods = [5, 10, 21]
            ema_data = self.indicators.calculate_multiple_ema(closes, ema_periods)
            indicators_data.update(ema_data)

            # SMA (10)
            if len(closes) >= 10:
                sma_10 = self.indicators.calculate_sma(closes, 10)
                indicators_data['sma_10'] = sma_10

            # MACD
            if len(closes) >= 26:
                macd_data = self.indicators.calculate_macd(closes, 12, 26, 9)
                indicators_data['macd'] = macd_data

            # Momentum (10)
            if len(closes) >= 11:
                momentum_values = self.indicators.calculate_momentum(closes, 10)
                indicators_data['momentum_10'] = momentum_values

            # Bollinger Bands (20, 2)
            if len(closes) >= 20:
                bollinger_data = self.indicators.calculate_bollinger_bands(closes, 20, 2)
                indicators_data['bollinger'] = bollinger_data

            # ATR متعدد الفترات (5, 14)
            atr_periods = [5, 14]
            atr_data = self.indicators.calculate_multiple_atr(highs, lows, closes, atr_periods)
            indicators_data.update(atr_data)

            # Heiken Ashi
            if len(closes) >= 2:
                ha_data = self.indicators.calculate_heiken_ashi(opens, highs, lows, closes)
                indicators_data['heiken_ashi'] = ha_data

            # Z-Score (20)
            if len(closes) >= 20:
                zscore_values = self.indicators.calculate_zscore(closes, 20)
                indicators_data['zscore_20'] = zscore_values

            # Stochastic
            if len(closes) >= 14:
                stoch_data = self.indicators.calculate_stochastic(closes, highs, lows, 14, 3)
                indicators_data['stochastic'] = stoch_data

            # ATR
            if len(closes) >= 14:
                atr_values = self.indicators.calculate_atr(highs, lows, closes, 14)
                indicators_data['atr'] = atr_values

            # ADX
            if len(closes) >= 14:
                adx_data = self.indicators.calculate_adx(highs, lows, closes, 14)
                indicators_data['adx'] = adx_data

            # Ichimoku
            if len(closes) >= 52:
                ichimoku_data = self.indicators.calculate_ichimoku(highs, lows, 9, 26, 52)
                indicators_data['ichimoku'] = ichimoku_data

            # إضافة المؤشرات لكل شمعة
            candles_with_indicators = []
            for i, candle in enumerate(fixed_candles):
                candle_with_indicators = candle.copy()
                candle_indicators = {}

                # حساب الفهرس الصحيح للمؤشرات
                # المؤشرات تبدأ من فترة معينة، لذا نحتاج لحساب الإزاحة

                # إضافة قيم المؤشرات المحسنة للشمعة الحالية

                # RSI متعدد الفترات (5, 14)
                for period in [5, 14]:
                    rsi_key = f"rsi_{period}"
                    if rsi_key in indicators_data and len(indicators_data[rsi_key]) > 0:
                        rsi_index = i - period
                        if rsi_index >= 0 and rsi_index < len(indicators_data[rsi_key]):
                            candle_indicators[rsi_key] = round(indicators_data[rsi_key][rsi_index], 5)

                # EMA متعدد الفترات (5, 10, 21)
                for period in [5, 10, 21]:
                    ema_key = f"ema_{period}"
                    if ema_key in indicators_data and len(indicators_data[ema_key]) > 0:
                        ema_index = i - (period - 1)
                        if ema_index >= 0 and ema_index < len(indicators_data[ema_key]):
                            candle_indicators[ema_key] = round(indicators_data[ema_key][ema_index], 5)

                # SMA (10)
                if 'sma_10' in indicators_data and len(indicators_data['sma_10']) > 0:
                    sma10_index = i - 9
                    if sma10_index >= 0 and sma10_index < len(indicators_data['sma_10']):
                        candle_indicators['sma_10'] = round(indicators_data['sma_10'][sma10_index], 5)

                # MACD
                if 'macd' in indicators_data:
                    macd_data = indicators_data['macd']
                    macd_index = i - 25
                    if macd_index >= 0 and macd_index < len(macd_data.get('macd', [])):
                        candle_indicators['macd'] = {
                            'macd': round(macd_data['macd'][macd_index], 6),
                            'signal': round(macd_data['signal'][macd_index], 6) if macd_index < len(macd_data.get('signal', [])) else None,
                            'histogram': round(macd_data['histogram'][macd_index], 6) if macd_index < len(macd_data.get('histogram', [])) else None
                        }

                # Momentum (10)
                if 'momentum_10' in indicators_data and len(indicators_data['momentum_10']) > 0:
                    momentum_index = i - 10
                    if momentum_index >= 0 and momentum_index < len(indicators_data['momentum_10']):
                        candle_indicators['momentum_10'] = round(indicators_data['momentum_10'][momentum_index], 5)

                # ATR متعدد الفترات (5, 14)
                for period in [5, 14]:
                    atr_key = f"atr_{period}"
                    if atr_key in indicators_data and len(indicators_data[atr_key]) > 0:
                        atr_index = i - (period - 1)
                        if atr_index >= 0 and atr_index < len(indicators_data[atr_key]):
                            candle_indicators[atr_key] = round(indicators_data[atr_key][atr_index], 6)

                # Heiken Ashi
                if 'heiken_ashi' in indicators_data:
                    ha_data = indicators_data['heiken_ashi']
                    if i < len(ha_data.get('ha_open', [])):
                        candle_indicators['heiken_ashi'] = {
                            'ha_open': round(ha_data['ha_open'][i], 5),
                            'ha_high': round(ha_data['ha_high'][i], 5),
                            'ha_low': round(ha_data['ha_low'][i], 5),
                            'ha_close': round(ha_data['ha_close'][i], 5)
                        }

                # Z-Score (20)
                if 'zscore_20' in indicators_data and len(indicators_data['zscore_20']) > 0:
                    zscore_index = i - 19
                    if zscore_index >= 0 and zscore_index < len(indicators_data['zscore_20']):
                        candle_indicators['zscore_20'] = round(indicators_data['zscore_20'][zscore_index], 5)

                # Bollinger Bands
                if 'bollinger' in indicators_data:
                    bollinger_data = indicators_data['bollinger']
                    # Bollinger يبدأ من الشمعة رقم 20
                    bb_index = i - 19
                    if bb_index >= 0 and bb_index < len(bollinger_data.get('upper', [])):
                        candle_indicators['bollinger'] = {
                            'upper': round(bollinger_data['upper'][bb_index], 5),
                            'middle': round(bollinger_data['middle'][bb_index], 5) if bb_index < len(bollinger_data.get('middle', [])) else None,
                            'lower': round(bollinger_data['lower'][bb_index], 5) if bb_index < len(bollinger_data.get('lower', [])) else None
                        }

                # Stochastic
                if 'stochastic' in indicators_data:
                    stoch_data = indicators_data['stochastic']
                    # Stochastic يبدأ من الشمعة رقم 14
                    stoch_index = i - 13
                    if stoch_index >= 0 and stoch_index < len(stoch_data.get('k', [])):
                        candle_indicators['stochastic'] = {
                            'k': round(stoch_data['k'][stoch_index], 2),
                            'd': round(stoch_data['d'][stoch_index], 2) if stoch_index < len(stoch_data.get('d', [])) else None
                        }

                # ATR
                if 'atr' in indicators_data and len(indicators_data['atr']) > 0:
                    # ATR يبدأ من الشمعة رقم 14
                    atr_index = i - 13
                    if atr_index >= 0 and atr_index < len(indicators_data['atr']):
                        candle_indicators['atr'] = round(indicators_data['atr'][atr_index], 6)

                # ADX
                if 'adx' in indicators_data:
                    adx_data = indicators_data['adx']
                    # ADX يبدأ من الشمعة رقم 14
                    adx_index = i - 13
                    if adx_index >= 0 and adx_index < len(adx_data.get('adx', [])):
                        candle_indicators['adx'] = {
                            'adx': round(adx_data['adx'][adx_index], 2),
                            'plus_di': round(adx_data['plus_di'][adx_index], 2) if adx_index < len(adx_data.get('plus_di', [])) else None,
                            'minus_di': round(adx_data['minus_di'][adx_index], 2) if adx_index < len(adx_data.get('minus_di', [])) else None
                        }

                # Ichimoku
                if 'ichimoku' in indicators_data:
                    ichimoku_data = indicators_data['ichimoku']
                    # Ichimoku يبدأ من الشمعة رقم 52
                    ichimoku_index = i - 51
                    if ichimoku_index >= 0 and ichimoku_index < len(ichimoku_data.get('tenkan', [])):
                        candle_indicators['ichimoku'] = {
                            'tenkan': round(ichimoku_data['tenkan'][ichimoku_index], 5),
                            'kijun': round(ichimoku_data['kijun'][ichimoku_index], 5) if ichimoku_index < len(ichimoku_data.get('kijun', [])) else None,
                            'senkou_a': round(ichimoku_data['senkou_a'][ichimoku_index], 5) if ichimoku_index < len(ichimoku_data.get('senkou_a', [])) else None,
                            'senkou_b': round(ichimoku_data['senkou_b'][ichimoku_index], 5) if ichimoku_index < len(ichimoku_data.get('senkou_b', [])) else None,
                            'chikou': round(ichimoku_data['chikou'][ichimoku_index], 5) if ichimoku_index < len(ichimoku_data.get('chikou', [])) else None
                        }

                # إضافة المؤشرات للشمعة
                if candle_indicators:
                    candle_with_indicators['indicators'] = candle_indicators

                candles_with_indicators.append(candle_with_indicators)

            return candles_with_indicators

        except Exception as e:
            logger.error(f"خطأ في معالجة الشموع مع المؤشرات لـ {asset_name}: {e}")
            return candles

    # تم حذف دالة المؤشرات المنفصلة - الآن المؤشرات تُضاف مباشرة للشموع

    def _get_rsi_signal(self, rsi_value: float) -> str:
        """تحديد إشارة RSI"""
        if rsi_value < 30:
            return "oversold"  # بيع مفرط
        elif rsi_value > 70:
            return "overbought"  # شراء مفرط
        else:
            return "neutral"  # متوسط

    def _get_macd_signal(self, macd_data: Dict) -> str:
        """تحديد إشارة MACD"""
        try:
            macd_line = macd_data.get('macd', [])
            signal_line = macd_data.get('signal', [])

            if macd_line and signal_line and len(macd_line) > 0 and len(signal_line) > 0:
                if macd_line[-1] > signal_line[-1]:
                    return "bullish"  # صاعد
                else:
                    return "bearish"  # هابط
            return "neutral"
        except:
            return "neutral"

    def _get_ma_trend(self, current_price: float, sma_20: List[float], ema_20: List[float]) -> str:
        """تحديد اتجاه المتوسطات المتحركة"""
        try:
            if sma_20 and ema_20:
                sma_current = sma_20[-1]
                ema_current = ema_20[-1]

                if current_price > sma_current and current_price > ema_current:
                    return "bullish"  # صاعد
                elif current_price < sma_current and current_price < ema_current:
                    return "bearish"  # هابط
                else:
                    return "sideways"  # جانبي
            return "neutral"
        except:
            return "neutral"

    def _get_bollinger_position(self, current_price: float, bollinger_data: Dict) -> str:
        """تحديد موقع السعر في نطاقات بولينجر"""
        try:
            upper = bollinger_data.get('upper', [])
            lower = bollinger_data.get('lower', [])

            if upper and lower:
                upper_current = upper[-1]
                lower_current = lower[-1]

                if current_price > upper_current:
                    return "above_upper"  # فوق النطاق العلوي
                elif current_price < lower_current:
                    return "below_lower"  # تحت النطاق السفلي
                else:
                    return "within_bands"  # داخل النطاق
            return "neutral"
        except:
            return "neutral"

    def _get_stochastic_signal(self, stoch_current: Dict) -> str:
        """تحديد إشارة المؤشر العشوائي"""
        try:
            k_value = stoch_current.get('k')
            d_value = stoch_current.get('d')

            if k_value is not None and d_value is not None:
                if k_value < 20 and d_value < 20:
                    return "oversold"  # بيع مفرط
                elif k_value > 80 and d_value > 80:
                    return "overbought"  # شراء مفرط
                else:
                    return "neutral"  # متوسط
            return "neutral"
        except:
            return "neutral"

    def _get_volatility_level(self, atr_value: float) -> str:
        """تحديد مستوى التقلب"""
        # هذه قيم تقريبية، يمكن تعديلها حسب الأصل
        if atr_value < 0.0010:
            return "low"  # منخفض
        elif atr_value < 0.0020:
            return "medium"  # متوسط
        else:
            return "high"  # عالي

    def _get_adx_strength(self, adx_value: float) -> str:
        """تحديد قوة الاتجاه من ADX"""
        if adx_value < 25:
            return "weak"  # ضعيف
        elif adx_value < 50:
            return "strong"  # قوي
        else:
            return "very_strong"  # قوي جداً

    def _get_ichimoku_signal(self, current_price: float, ichimoku_data: Dict) -> str:
        """تحديد إشارة سحابة إيشيموكو"""
        try:
            current = ichimoku_data.get('current', {})
            senkou_a = current.get('senkou_a')
            senkou_b = current.get('senkou_b')

            if senkou_a is not None and senkou_b is not None:
                cloud_top = max(senkou_a, senkou_b)
                cloud_bottom = min(senkou_a, senkou_b)

                if current_price > cloud_top:
                    return "above_cloud"  # فوق السحابة
                elif current_price < cloud_bottom:
                    return "below_cloud"  # تحت السحابة
                else:
                    return "in_cloud"  # داخل السحابة
            return "neutral"
        except:
            return "neutral"

    def _calculate_overall_signal(self, indicators: Dict) -> Dict[str, Any]:
        """حساب الإشارة العامة المجمعة"""
        try:
            signals = {
                'bullish': 0,
                'bearish': 0,
                'neutral': 0
            }

            # تجميع الإشارات من جميع المؤشرات
            if 'rsi' in indicators:
                rsi_signal = indicators['rsi'].get('signal', 'neutral')
                if rsi_signal == 'oversold':
                    signals['bullish'] += 1
                elif rsi_signal == 'overbought':
                    signals['bearish'] += 1
                else:
                    signals['neutral'] += 1

            if 'macd' in indicators:
                macd_trend = indicators['macd'].get('trend', 'neutral')
                if macd_trend == 'bullish':
                    signals['bullish'] += 1
                elif macd_trend == 'bearish':
                    signals['bearish'] += 1
                else:
                    signals['neutral'] += 1

            if 'moving_averages' in indicators:
                ma_trend = indicators['moving_averages'].get('trend', 'neutral')
                if ma_trend == 'bullish':
                    signals['bullish'] += 1
                elif ma_trend == 'bearish':
                    signals['bearish'] += 1
                else:
                    signals['neutral'] += 1

            # تحديد الإشارة الغالبة
            max_signal = max(signals, key=signals.get)
            confidence = signals[max_signal] / sum(signals.values()) if sum(signals.values()) > 0 else 0

            return {
                'signal': max_signal,
                'confidence': round(confidence * 100, 2),
                'breakdown': signals,
                'recommendation': self._get_recommendation(max_signal, confidence)
            }

        except Exception as e:
            logger.error(f"خطأ في حساب الإشارة العامة: {e}")
            return {
                'signal': 'neutral',
                'confidence': 0,
                'breakdown': {'bullish': 0, 'bearish': 0, 'neutral': 1},
                'recommendation': 'hold'
            }

    def _get_recommendation(self, signal: str, confidence: float) -> str:
        """تحديد التوصية بناءً على الإشارة والثقة"""
        if confidence < 0.6:  # ثقة أقل من 60%
            return "hold"  # انتظار
        elif signal == "bullish":
            return "buy"  # شراء
        elif signal == "bearish":
            return "sell"  # بيع
        else:
            return "hold"  # انتظار

    async def _start_realtime_subscriptions(self):
        """بدء الاشتراك في البيانات المباشرة لجميع الأزواج"""
        logger.info("📡 بدء الاشتراك في البيانات المباشرة...")

        # تحميل قائمة الأزواج التقليدية
        traditional_assets = self.data_manager.load_traditional_assets()

        if not traditional_assets:
            logger.warning("لا توجد أزواج تقليدية للاشتراك فيها")
            return

        for asset_name in traditional_assets.keys():
            try:
                # التحقق من أن الأصل مفتوح
                asset_data = traditional_assets[asset_name]
                if not asset_data.get('is_open', False):
                    logger.debug(f"⏸️ تخطي {asset_name} - الأصل مغلق")
                    continue

                # الاشتراك في الشموع المباشرة باستخدام الدالة الصحيحة
                self.client.start_candles_stream(asset_name, self.timeframe)
                self.active_subscriptions.add(asset_name)

                logger.info(f"✅ تم الاشتراك في البيانات المباشرة لـ {asset_name}")

                # تأخير قصير لتجنب إرهاق الخادم
                await asyncio.sleep(0.2)

            except Exception as e:
                logger.error(f"خطأ في الاشتراك في البيانات المباشرة لـ {asset_name}: {e}")

        logger.info(f"✅ تم الاشتراك في {len(self.active_subscriptions)} أصل")

    async def _main_monitoring_loop(self):
        """الحلقة الرئيسية لمراقبة النظام"""
        logger.info("🔄 بدء الحلقة الرئيسية للمراقبة...")

        last_connection_check = time.time()
        last_assets_update = time.time()
        last_profile_update = time.time()

        while self.is_running:
            try:
                current_time = time.time()

                # فحص الاتصال كل دقيقة
                if current_time - last_connection_check > 60:
                    await self._check_connection_health()
                    last_connection_check = current_time

                # تحديث نسب الربح كل 5 دقائق
                if current_time - last_assets_update > 300:
                    await self._update_assets_payouts()
                    last_assets_update = current_time

                # تحديث بيانات الملف الشخصي كل 30 دقيقة
                if current_time - last_profile_update > 1800:
                    await self._fetch_profile_data()
                    # تحديث بيانات الحساب أيضاً
                    await self.update_account_data()
                    last_profile_update = current_time

                # معالجة البيانات المباشرة
                await self._process_realtime_data()

                # انتظار قصير
                await asyncio.sleep(1)

            except Exception as e:
                logger.error(f"خطأ في الحلقة الرئيسية: {e}")
                await asyncio.sleep(5)

    async def _check_connection_health(self):
        """فحص صحة الاتصال"""
        try:
            is_connected = await self.client.check_connect()

            if not is_connected:
                logger.warning("⚠️ انقطع الاتصال، محاولة إعادة الاتصال...")
                self.is_connected = False

                # محاولة إعادة الاتصال
                success = await self._connect_with_retry()

                if success:
                    # إعادة الاشتراك في البيانات المباشرة
                    await self._restart_realtime_subscriptions()
                else:
                    logger.error("فشل في إعادة الاتصال")

        except Exception as e:
            logger.error(f"خطأ في فحص الاتصال: {e}")

    async def _restart_realtime_subscriptions(self):
        """إعادة تشغيل الاشتراكات المباشرة"""
        logger.info("🔄 إعادة تشغيل الاشتراكات المباشرة...")

        self.active_subscriptions.clear()
        await self._start_realtime_subscriptions()

    async def _update_assets_payouts(self):
        """تحديث نسب الربح للأزواج"""
        try:
            logger.info("💰 تحديث نسب الربح...")

            updated_count = 0
            for asset_name in self.traditional_assets.keys():
                try:
                    # جلب نسبة الربح الحالية
                    payout = self.client.get_payout_by_asset(asset_name)

                    # تحديث البيانات إذا تغيرت النسبة
                    if self.traditional_assets[asset_name]['payout'] != payout:
                        self.traditional_assets[asset_name]['payout'] = payout
                        self.traditional_assets[asset_name]['last_update'] = datetime.now().isoformat()
                        updated_count += 1

                except Exception as e:
                    logger.warning(f"خطأ في تحديث نسبة الربح لـ {asset_name}: {e}")

            # حفظ التحديثات
            if updated_count > 0:
                self.data_manager.save_traditional_assets(self.traditional_assets)
                logger.info(f"✅ تم تحديث نسب الربح لـ {updated_count} أصل")

        except Exception as e:
            logger.error(f"خطأ في تحديث نسب الربح: {e}")

    async def _process_realtime_data(self):
        """معالجة البيانات المباشرة المحسنة"""
        try:
            for asset_name in self.active_subscriptions:
                try:
                    # جلب آخر شمعة مباشرة باستخدام الطريقة الصحيحة
                    # التحقق من وجود بيانات مباشرة في API
                    if hasattr(self.client.api, 'realtime_candles') and asset_name in self.client.api.realtime_candles:
                        realtime_data = self.client.api.realtime_candles[asset_name]

                        # تحويل البيانات إلى تنسيق الشمعة
                        if realtime_data and len(realtime_data) >= 4:
                            latest_candle = {
                                'open': realtime_data[2],
                                'high': realtime_data[2],
                                'low': realtime_data[2],
                                'close': realtime_data[2],
                                'volume': 0,
                                'timestamp': realtime_data[1]
                            }
                        else:
                            continue

                    else:
                        # محاولة جلب البيانات بالطريقة التقليدية
                        realtime_candles = await self.client.get_realtime_candles(asset_name)
                        if realtime_candles and len(realtime_candles) > 0:
                            latest_candle = realtime_candles[-1]
                        else:
                            continue

                    if latest_candle:
                        # التحقق من صحة بيانات الشمعة
                        if not self._validate_candle_data(latest_candle):
                            logger.warning(f"بيانات شمعة غير صحيحة لـ {asset_name}")
                            continue

                        # إصلاح التوقيت وإضافة المؤشرات للشمعة المباشرة
                        processed_candle = await self._process_single_candle_with_indicators(asset_name, latest_candle)

                        # حفظ الشمعة المباشرة مع المؤشرات
                        self.data_manager.save_realtime_candle(asset_name, processed_candle, self.timeframe)

                        # التحقق من إغلاق الشمعة
                        if self._is_candle_closed(processed_candle):
                            await self._handle_closed_candle(asset_name, processed_candle)
                            logger.debug(f"🕯️ شمعة مغلقة لـ {asset_name} في {processed_candle.get('datetime', 'N/A')}")

                except Exception as asset_error:
                    logger.error(f"خطأ في معالجة البيانات المباشرة لـ {asset_name}: {asset_error}")
                    continue

        except Exception as e:
            logger.error(f"خطأ عام في معالجة البيانات المباشرة: {e}")

    def _validate_candle_data(self, candle: Dict) -> bool:
        """التحقق من صحة بيانات الشمعة"""
        try:
            required_fields = ['open', 'high', 'low', 'close']
            for field in required_fields:
                if field not in candle or candle[field] is None:
                    return False

                # التحقق من أن القيم رقمية وموجبة
                value = float(candle[field])
                if value <= 0:
                    return False

            # التحقق من منطقية البيانات (high >= low, etc.)
            high = float(candle['high'])
            low = float(candle['low'])
            open_price = float(candle['open'])
            close_price = float(candle['close'])

            if high < low or high < open_price or high < close_price or low > open_price or low > close_price:
                return False

            return True

        except (ValueError, TypeError):
            return False

    async def _process_single_candle_with_indicators(self, asset_name: str, candle: Dict) -> Dict:
        """معالجة شمعة واحدة مع إضافة المؤشرات وإصلاح التوقيت"""
        try:
            # إصلاح التوقيت
            processed_candle = candle.copy()
            current_time = int(time.time())

            # تقريب التوقيت لأقرب فترة زمنية (5 دقائق)
            # التأكد من أن الشمعة تبدأ في الدقائق الصحيحة (00, 05, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55)
            candle_time = (current_time // self.timeframe) * self.timeframe
            processed_candle['time'] = candle_time
            processed_candle['timestamp'] = candle_time

            # إضافة تنسيق مقروء للوقت
            readable_time = datetime.fromtimestamp(candle_time).strftime('%Y-%m-%d %H:%M:%S')
            processed_candle['datetime'] = readable_time

            # جلب البيانات التاريخية لحساب المؤشرات
            historical_candles = self.data_manager.load_historical_candles(asset_name, self.timeframe)

            if historical_candles and len(historical_candles) >= 20:
                # دمج البيانات التاريخية مع الشمعة الحالية
                all_candles = historical_candles + [processed_candle]

                # استخراج البيانات للمؤشرات
                closes = [float(c.get('close', 0)) for c in all_candles]
                highs = [float(c.get('high', 0)) for c in all_candles]
                lows = [float(c.get('low', 0)) for c in all_candles]
                opens = [float(c.get('open', 0)) for c in all_candles]

                # حساب المؤشرات المحسنة للشمعة الحالية (آخر قيمة)
                candle_indicators = {}

                # RSI متعدد الفترات (5, 14)
                for period in [5, 14]:
                    if len(closes) >= period:
                        rsi_values = self.indicators.calculate_rsi(closes, period)
                        if rsi_values:
                            candle_indicators[f'rsi_{period}'] = round(rsi_values[-1], 5)

                # EMA متعدد الفترات (5, 10, 21)
                for period in [5, 10, 21]:
                    if len(closes) >= period:
                        ema_values = self.indicators.calculate_ema(closes, period)
                        if ema_values:
                            candle_indicators[f'ema_{period}'] = round(ema_values[-1], 5)

                # SMA (10)
                if len(closes) >= 10:
                    sma_values = self.indicators.calculate_sma(closes, 10)
                    if sma_values:
                        candle_indicators['sma_10'] = round(sma_values[-1], 5)

                # MACD
                if len(closes) >= 26:
                    macd_data = self.indicators.calculate_macd(closes, 12, 26, 9)
                    if macd_data:
                        candle_indicators['macd'] = {
                            'macd': round(macd_data['macd'][-1], 6) if macd_data.get('macd') else None,
                            'signal': round(macd_data['signal'][-1], 6) if macd_data.get('signal') else None,
                            'histogram': round(macd_data['histogram'][-1], 6) if macd_data.get('histogram') else None
                        }

                # Momentum (10)
                if len(closes) >= 11:
                    momentum_values = self.indicators.calculate_momentum(closes, 10)
                    if momentum_values:
                        candle_indicators['momentum_10'] = round(momentum_values[-1], 5)

                # Bollinger Bands (20, 2)
                if len(closes) >= 20:
                    bollinger_data = self.indicators.calculate_bollinger_bands(closes, 20, 2)
                    if bollinger_data:
                        candle_indicators['bollinger_bands'] = {
                            'upper': round(bollinger_data['upper'][-1], 5) if bollinger_data.get('upper') else None,
                            'middle': round(bollinger_data['middle'][-1], 5) if bollinger_data.get('middle') else None,
                            'lower': round(bollinger_data['lower'][-1], 5) if bollinger_data.get('lower') else None
                        }

                # ATR متعدد الفترات (5, 14)
                for period in [5, 14]:
                    if len(closes) >= period:
                        atr_values = self.indicators.calculate_atr(highs, lows, closes, period)
                        if atr_values:
                            candle_indicators[f'atr_{period}'] = round(atr_values[-1], 6)

                # Heiken Ashi
                if len(closes) >= 2:
                    ha_data = self.indicators.calculate_heiken_ashi(opens, highs, lows, closes)
                    if ha_data:
                        candle_indicators['heiken_ashi'] = {
                            'ha_open': round(ha_data['ha_open'][-1], 5) if ha_data.get('ha_open') else None,
                            'ha_high': round(ha_data['ha_high'][-1], 5) if ha_data.get('ha_high') else None,
                            'ha_low': round(ha_data['ha_low'][-1], 5) if ha_data.get('ha_low') else None,
                            'ha_close': round(ha_data['ha_close'][-1], 5) if ha_data.get('ha_close') else None
                        }

                # Z-Score (20)
                if len(closes) >= 20:
                    zscore_values = self.indicators.calculate_zscore(closes, 20)
                    if zscore_values:
                        candle_indicators['zscore_20'] = round(zscore_values[-1], 5)

                # Stochastic
                if len(closes) >= 14:
                    stoch_data = self.indicators.calculate_stochastic(closes, highs, lows, 14, 3)
                    if stoch_data:
                        candle_indicators['stochastic'] = {
                            'k': round(stoch_data['k'][-1], 5) if stoch_data.get('k') else None,
                            'd': round(stoch_data['d'][-1], 5) if stoch_data.get('d') else None
                        }

                # ATR
                if len(closes) >= 14:
                    atr_values = self.indicators.calculate_atr(highs, lows, closes, 14)
                    if atr_values:
                        candle_indicators['atr'] = atr_values[-1]

                # ADX
                if len(closes) >= 14:
                    adx_data = self.indicators.calculate_adx(highs, lows, closes, 14)
                    if adx_data:
                        candle_indicators['adx'] = {
                            'adx': adx_data['adx'][-1] if adx_data.get('adx') else None,
                            'plus_di': adx_data['plus_di'][-1] if adx_data.get('plus_di') else None,
                            'minus_di': adx_data['minus_di'][-1] if adx_data.get('minus_di') else None
                        }

                # Ichimoku
                if len(closes) >= 52:
                    ichimoku_data = self.indicators.calculate_ichimoku(highs, lows, 9, 26, 52)
                    if ichimoku_data:
                        candle_indicators['ichimoku'] = {
                            'tenkan': ichimoku_data['tenkan'][-1] if ichimoku_data.get('tenkan') else None,
                            'kijun': ichimoku_data['kijun'][-1] if ichimoku_data.get('kijun') else None,
                            'senkou_a': ichimoku_data['senkou_a'][-1] if ichimoku_data.get('senkou_a') else None,
                            'senkou_b': ichimoku_data['senkou_b'][-1] if ichimoku_data.get('senkou_b') else None,
                            'chikou': ichimoku_data['chikou'][-1] if ichimoku_data.get('chikou') else None
                        }

                # إضافة المؤشرات للشمعة
                if candle_indicators:
                    processed_candle['indicators'] = candle_indicators

            return processed_candle

        except Exception as e:
            logger.error(f"خطأ في معالجة الشمعة مع المؤشرات لـ {asset_name}: {e}")
            return candle

    def _is_candle_closed(self, candle: Dict) -> bool:
        """التحقق من إغلاق الشمعة"""
        try:
            candle_time = candle.get('timestamp', 0)
            current_time = time.time()

            # حساب بداية الشمعة التالية
            next_candle_start = ((candle_time // self.timeframe) + 1) * self.timeframe

            return current_time >= next_candle_start

        except Exception:
            return False

    async def _handle_closed_candle(self, asset_name: str, closed_candle: Dict):
        """معالجة الشمعة المغلقة مع تحديث محسن للمؤشرات"""
        try:
            # التأكد من أن الشمعة تحتوي على المؤشرات
            if 'indicators' not in closed_candle:
                # إعادة حساب المؤشرات للشمعة المغلقة
                historical_candles = self.data_manager.load_historical_candles(asset_name, self.timeframe)
                if historical_candles:
                    # دمج الشمعة المغلقة مع البيانات التاريخية لحساب المؤشرات
                    all_candles = historical_candles + [closed_candle]
                    processed_candles = await self._process_candles_with_indicators(asset_name, all_candles)
                    if processed_candles:
                        closed_candle = processed_candles[-1]  # أخذ الشمعة الأخيرة مع المؤشرات

            # إضافة الشمعة المغلقة للبيانات التاريخية
            self.data_manager.update_historical_candles(asset_name, [closed_candle], self.timeframe)

            # حفظ المؤشرات المحدثة
            if 'indicators' in closed_candle:
                self.data_manager.save_indicators_data(asset_name, closed_candle['indicators'], self.timeframe)

            # مسح البيانات المباشرة للشمعة المغلقة
            realtime_file = self.data_manager.realtime_dir / f"{asset_name}_{self.timeframe}s_realtime.json"
            if realtime_file.exists():
                try:
                    realtime_file.unlink()  # حذف ملف البيانات المباشرة
                except Exception:
                    pass

            logger.info(f"✅ تم معالجة شمعة مغلقة مع المؤشرات لـ {asset_name}")

        except Exception as e:
            logger.error(f"خطأ في معالجة الشمعة المغلقة لـ {asset_name}: {e}")

    def get_system_status(self) -> Dict[str, Any]:
        """الحصول على حالة النظام"""
        status = {
            'is_running': self.is_running,
            'is_connected': self.is_connected,
            'connection_attempts': self.connection_attempts,
            'traditional_assets_count': len(self.traditional_assets),
            'active_subscriptions_count': len(self.active_subscriptions),
            'cache_stats': self.data_manager.get_cache_stats(),
            'last_update': datetime.now().isoformat()
        }

        # إضافة معلومات الحساب إذا كان متاحاً
        if self.account_manager:
            status['account_info'] = {
                'current_account_type': self.account_manager.current_account_type,
                'current_balance': self.account_manager.current_balance
            }

        return status

    async def switch_account_type(self, account_type: str) -> bool:
        """تبديل نوع الحساب"""
        if not self.account_manager:
            logger.error("مدير الحسابات غير متاح")
            return False

        return await self.account_manager.switch_account(account_type)

    def get_account_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص الحسابات"""
        if not self.account_manager:
            return {"error": "مدير الحسابات غير متاح"}

        return self.account_manager.get_account_summary()

    async def update_account_data(self):
        """تحديث بيانات الحساب"""
        if self.account_manager:
            await self.account_manager.update_account_data()
