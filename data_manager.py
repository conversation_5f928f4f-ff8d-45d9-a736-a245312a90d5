"""
نظام إدارة البيانات المتقدم لـ PyQuotex
يدير البيانات التاريخية والمباشرة والمؤشرات التقنية
"""

import os
import json
import time
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
import threading
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

class DataManager:
    """مدير البيانات الرئيسي للنظام"""
    
    def __init__(self):
        self.data_dir = Path("data")
        self.historical_dir = self.data_dir / "historical"
        self.realtime_dir = self.data_dir / "realtime"
        self.indicators_dir = self.data_dir / "indicators"
        self.assets_file = self.data_dir / "traditional_assets.json"
        self.profile_file = self.data_dir / "profile_data.json"
        
        # إنشاء المجلدات إذا لم تكن موجودة
        self._create_directories()
        
        # قفل للحماية من التداخل
        self.lock = threading.Lock()
        
        # تخزين مؤقت للبيانات
        self.cache = {
            'assets': {},
            'historical': {},
            'realtime': {},
            'indicators': {}
        }
        
    def _create_directories(self):
        """إنشاء المجلدات المطلوبة"""
        for directory in [self.data_dir, self.historical_dir, 
                         self.realtime_dir, self.indicators_dir]:
            directory.mkdir(parents=True, exist_ok=True)
            
    def save_traditional_assets(self, assets_data: Dict[str, Any]):
        """حفظ قائمة الأزواج التقليدية مع نسب الربح"""
        try:
            with self.lock:
                # تحديث البيانات مع الطابع الزمني
                data = {
                    'timestamp': time.time(),
                    'last_update': datetime.now().isoformat(),
                    'assets': assets_data
                }
                
                with open(self.assets_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                
                # تحديث الكاش
                self.cache['assets'] = data
                
                logger.info(f"تم حفظ {len(assets_data)} زوج تقليدي")
                
        except Exception as e:
            logger.error(f"خطأ في حفظ الأزواج التقليدية: {e}")
            
    def load_traditional_assets(self) -> Dict[str, Any]:
        """تحميل قائمة الأزواج التقليدية"""
        try:
            if self.assets_file.exists():
                with open(self.assets_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.cache['assets'] = data
                    return data.get('assets', {})
            return {}
        except Exception as e:
            logger.error(f"خطأ في تحميل الأزواج التقليدية: {e}")
            return {}
            
    def save_profile_data(self, profile_data: Dict[str, Any]):
        """حفظ بيانات الملف الشخصي والحسابات"""
        try:
            with self.lock:
                data = {
                    'timestamp': time.time(),
                    'last_update': datetime.now().isoformat(),
                    'profile': profile_data
                }
                
                with open(self.profile_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                    
                logger.info("تم حفظ بيانات الملف الشخصي")
                
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات الملف الشخصي: {e}")
            
    def save_historical_candles(self, asset: str, candles: List[Dict], timeframe: int = 300):
        """حفظ البيانات التاريخية للشموع"""
        try:
            file_path = self.historical_dir / f"{asset}_{timeframe}s.json"
            
            with self.lock:
                # تحضير البيانات
                data = {
                    'asset': asset,
                    'timeframe': timeframe,
                    'timestamp': time.time(),
                    'last_update': datetime.now().isoformat(),
                    'candles_count': len(candles),
                    'candles': candles
                }
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                
                # تحديث الكاش
                cache_key = f"{asset}_{timeframe}"
                self.cache['historical'][cache_key] = data
                
                logger.info(f"تم حفظ {len(candles)} شمعة تاريخية لـ {asset}")
                
        except Exception as e:
            logger.error(f"خطأ في حفظ البيانات التاريخية لـ {asset}: {e}")
            
    def load_historical_candles(self, asset: str, timeframe: int = 300) -> List[Dict]:
        """تحميل البيانات التاريخية للشموع"""
        try:
            file_path = self.historical_dir / f"{asset}_{timeframe}s.json"
            cache_key = f"{asset}_{timeframe}"
            
            # التحقق من الكاش أولاً
            if cache_key in self.cache['historical']:
                return self.cache['historical'][cache_key].get('candles', [])
            
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.cache['historical'][cache_key] = data
                    return data.get('candles', [])
            return []
            
        except Exception as e:
            logger.error(f"خطأ في تحميل البيانات التاريخية لـ {asset}: {e}")
            return []
            
    def get_last_candle_timestamp(self, asset: str, timeframe: int = 300) -> Optional[float]:
        """الحصول على timestamp آخر شمعة محفوظة"""
        try:
            candles = self.load_historical_candles(asset, timeframe)
            if candles:
                # البحث عن آخر شمعة (أحدث timestamp)
                return max(candle.get('timestamp', 0) for candle in candles)
            return None
        except Exception as e:
            logger.error(f"خطأ في الحصول على آخر timestamp لـ {asset}: {e}")
            return None
            
    def update_historical_candles(self, asset: str, new_candles: List[Dict], timeframe: int = 300):
        """تحديث البيانات التاريخية بشموع جديدة"""
        try:
            existing_candles = self.load_historical_candles(asset, timeframe)
            
            # دمج الشموع الجديدة مع الموجودة
            all_candles = existing_candles + new_candles
            
            # إزالة التكرارات بناءً على timestamp
            unique_candles = {}
            for candle in all_candles:
                timestamp = candle.get('timestamp')
                if timestamp:
                    unique_candles[timestamp] = candle
                    
            # ترتيب الشموع حسب التوقيت
            sorted_candles = sorted(unique_candles.values(), 
                                  key=lambda x: x.get('timestamp', 0))
            
            # الاحتفاظ بآخر 500 شمعة فقط
            if len(sorted_candles) > 500:
                sorted_candles = sorted_candles[-500:]
                
            # حفظ البيانات المحدثة
            self.save_historical_candles(asset, sorted_candles, timeframe)
            
            logger.info(f"تم تحديث البيانات التاريخية لـ {asset} بـ {len(new_candles)} شمعة جديدة")
            
        except Exception as e:
            logger.error(f"خطأ في تحديث البيانات التاريخية لـ {asset}: {e}")
            
    def save_realtime_candle(self, asset: str, candle: Dict, timeframe: int = 300):
        """حفظ شمعة مباشرة"""
        try:
            file_path = self.realtime_dir / f"{asset}_{timeframe}s_realtime.json"
            
            with self.lock:
                data = {
                    'asset': asset,
                    'timeframe': timeframe,
                    'timestamp': time.time(),
                    'last_update': datetime.now().isoformat(),
                    'current_candle': candle
                }
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                
                # تحديث الكاش
                cache_key = f"{asset}_{timeframe}_realtime"
                self.cache['realtime'][cache_key] = data
                
        except Exception as e:
            logger.error(f"خطأ في حفظ الشمعة المباشرة لـ {asset}: {e}")
            
    def save_indicators_data(self, asset: str, indicators: Dict, timeframe: int = 300):
        """حفظ بيانات المؤشرات التقنية"""
        try:
            file_path = self.indicators_dir / f"{asset}_{timeframe}s_indicators.json"
            
            with self.lock:
                data = {
                    'asset': asset,
                    'timeframe': timeframe,
                    'timestamp': time.time(),
                    'last_update': datetime.now().isoformat(),
                    'indicators': indicators
                }
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                
                # تحديث الكاش
                cache_key = f"{asset}_{timeframe}_indicators"
                self.cache['indicators'][cache_key] = data
                
        except Exception as e:
            logger.error(f"خطأ في حفظ المؤشرات لـ {asset}: {e}")
            
    def get_cache_stats(self) -> Dict[str, int]:
        """إحصائيات الكاش"""
        return {
            'assets_cached': len(self.cache['assets']),
            'historical_cached': len(self.cache['historical']),
            'realtime_cached': len(self.cache['realtime']),
            'indicators_cached': len(self.cache['indicators'])
        }
        
    def clear_cache(self):
        """مسح الكاش"""
        with self.lock:
            self.cache = {
                'assets': {},
                'historical': {},
                'realtime': {},
                'indicators': {}
            }
            logger.info("تم مسح الكاش")
