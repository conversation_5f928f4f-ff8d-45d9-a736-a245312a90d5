"""
استراتيجية السكالبينغ الاحترافية الموحدة
تدمج التحليل الفني والكمي والسلوكي والذكاء الاصطناعي
"""

import time
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import json
from datetime import datetime

from technical_analysis_engine import TechnicalAnalysisEngine
from quantitative_analysis_engine import QuantitativeAnalysisEngine
from behavioral_analysis_engine import BehavioralAnalysisEngine
from ai_analysis_engine import AIAnalysisEngine

logger = logging.getLogger(__name__)

class SignalDecision(Enum):
    """قرار الإشارة النهائي"""
    STRONG_CALL = "STRONG_CALL"
    CALL = "CALL"
    WEAK_CALL = "WEAK_CALL"
    HOLD = "HOLD"
    WEAK_PUT = "WEAK_PUT"
    PUT = "PUT"
    STRONG_PUT = "STRONG_PUT"

class MarketCondition(Enum):
    """حالة السوق"""
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    SIDEWAYS = "sideways"
    VOLATILE = "volatile"
    STABLE = "stable"

@dataclass
class TradingSignal:
    """إشارة التداول النهائية"""
    decision: SignalDecision
    confidence: float
    timeframe: int  # بالثواني
    entry_price: float
    market_condition: MarketCondition
    risk_level: str
    analysis_breakdown: Dict[str, Any]
    timestamp: str

class ProfessionalScalpingStrategy:
    """استراتيجية السكالبينغ الاحترافية الموحدة"""
    
    def __init__(self):
        # محركات التحليل
        self.technical_engine = TechnicalAnalysisEngine()
        self.quantitative_engine = QuantitativeAnalysisEngine()
        self.behavioral_engine = BehavioralAnalysisEngine()
        self.ai_engine = AIAnalysisEngine()
        
        # أوزان طبقات التحليل
        self.layer_weights = {
            'technical': 0.30,
            'quantitative': 0.25,
            'behavioral': 0.25,
            'ai': 0.20
        }
        
        # عتبات اتخاذ القرار
        self.decision_thresholds = {
            'strong_signal': 0.85,
            'moderate_signal': 0.75,
            'weak_signal': 0.65,
            'minimum_confidence': 0.60
        }
        
        # إعدادات الإطار الزمني
        self.timeframe_settings = {
            'very_strong': 300,  # 5 دقائق
            'strong': 240,       # 4 دقائق
            'moderate': 180,     # 3 دقائق
            'weak': 120,         # دقيقتان
            'minimum': 60        # دقيقة واحدة
        }
        
        # فلاتر السوق
        self.market_filters = {
            'min_volatility': 0.001,
            'max_volatility': 0.05,
            'min_volume_ratio': 0.5,
            'max_spread': 0.0005
        }
        
        # إحصائيات الأداء
        self.performance_stats = {
            'total_signals': 0,
            'successful_signals': 0,
            'failed_signals': 0,
            'win_rate': 0.0,
            'avg_confidence': 0.0,
            'layer_contributions': {
                'technical': 0.0,
                'quantitative': 0.0,
                'behavioral': 0.0,
                'ai': 0.0
            }
        }
        
    def analyze_market_comprehensive(self, candles: List[Dict], indicators: Dict,
                                   volume_data: List[float] = None,
                                   historical_signals: List[Dict] = None) -> Optional[TradingSignal]:
        """التحليل الشامل للسوق وإنتاج إشارة التداول"""
        try:
            if len(candles) < 30:
                logger.warning("عدد الشموع غير كافي للتحليل الشامل")
                return None
            
            # 1. التحليل الفني
            logger.debug("بدء التحليل الفني...")
            technical_analysis = self.technical_engine.analyze_market(candles, indicators)
            
            # 2. التحليل الكمي
            logger.debug("بدء التحليل الكمي...")
            quantitative_analysis = self.quantitative_engine.analyze_market(
                candles, indicators, historical_signals
            )
            
            # 3. التحليل السلوكي
            logger.debug("بدء التحليل السلوكي...")
            behavioral_analysis = self.behavioral_engine.analyze_market(
                candles, indicators, volume_data
            )
            
            # 4. تحليل الذكاء الاصطناعي
            logger.debug("بدء تحليل الذكاء الاصطناعي...")
            ai_analysis = self.ai_engine.analyze_market(
                candles, indicators, technical_analysis, 
                quantitative_analysis, behavioral_analysis
            )
            
            # 5. دمج جميع التحليلات
            logger.debug("دمج التحليلات...")
            final_signal = self._synthesize_analyses(
                technical_analysis, quantitative_analysis, 
                behavioral_analysis, ai_analysis, candles
            )
            
            # 6. تطبيق فلاتر السوق
            if final_signal and not self._apply_market_filters(final_signal, candles):
                logger.debug("تم رفض الإشارة بواسطة فلاتر السوق")
                return None
            
            # 7. تحديث الإحصائيات
            if final_signal:
                self._update_performance_stats(final_signal)
            
            return final_signal
            
        except Exception as e:
            logger.error(f"خطأ في التحليل الشامل: {e}")
            return None
    
    def _synthesize_analyses(self, technical: Dict, quantitative: Dict, 
                           behavioral: Dict, ai: Dict, candles: List[Dict]) -> Optional[TradingSignal]:
        """دمج وتوليف جميع التحليلات"""
        try:
            # حساب النتائج المرجحة لكل طبقة
            layer_scores = self._calculate_layer_scores(technical, quantitative, behavioral, ai)
            
            # حساب النتيجة الإجمالية
            overall_score = self._calculate_overall_score(layer_scores)
            
            # تحديد الاتجاه الغالب
            dominant_direction = self._determine_dominant_direction(technical, quantitative, behavioral, ai)
            
            # حساب مستوى الثقة
            confidence_level = self._calculate_confidence_level(layer_scores, overall_score)
            
            # التحقق من الحد الأدنى للثقة
            if confidence_level < self.decision_thresholds['minimum_confidence']:
                logger.debug(f"مستوى الثقة منخفض: {confidence_level:.3f}")
                return None
            
            # تحديد قوة الإشارة
            signal_strength = self._determine_signal_strength(confidence_level)
            
            # تحديد القرار النهائي
            final_decision = self._make_final_decision(dominant_direction, signal_strength)
            
            # تحديد الإطار الزمني
            optimal_timeframe = self._determine_optimal_timeframe(signal_strength, confidence_level)
            
            # تحليل حالة السوق
            market_condition = self._analyze_market_condition(candles, technical, quantitative)
            
            # تقييم المخاطر
            risk_level = self._assess_risk_level(layer_scores, market_condition)
            
            # إنشاء الإشارة النهائية
            trading_signal = TradingSignal(
                decision=final_decision,
                confidence=confidence_level,
                timeframe=optimal_timeframe,
                entry_price=candles[-1]['close'],
                market_condition=market_condition,
                risk_level=risk_level,
                analysis_breakdown={
                    'technical': technical,
                    'quantitative': quantitative,
                    'behavioral': behavioral,
                    'ai': ai,
                    'layer_scores': layer_scores,
                    'overall_score': overall_score,
                    'dominant_direction': dominant_direction
                },
                timestamp=datetime.now().isoformat()
            )
            
            logger.info(f"🎯 إشارة تداول: {final_decision.value} | ثقة: {confidence_level:.1%} | زمن: {optimal_timeframe}s")
            
            return trading_signal
            
        except Exception as e:
            logger.error(f"خطأ في توليف التحليلات: {e}")
            return None
    
    def _calculate_layer_scores(self, technical: Dict, quantitative: Dict, 
                              behavioral: Dict, ai: Dict) -> Dict[str, float]:
        """حساب نتائج كل طبقة تحليل"""
        try:
            scores = {}
            
            # نتيجة التحليل الفني
            if technical.get('direction') != 'HOLD':
                ta_confidence = technical.get('confidence', 0) / 100
                ta_quality = technical.get('quality_score', 0.5)
                scores['technical'] = (ta_confidence + ta_quality) / 2
            else:
                scores['technical'] = 0.0
            
            # نتيجة التحليل الكمي
            qa_score = quantitative.get('overall_score', 0)
            qa_confidence = quantitative.get('confidence', 0)
            scores['quantitative'] = (qa_score + qa_confidence) / 2
            
            # نتيجة التحليل السلوكي
            ba_score = behavioral.get('behavioral_score', 0)
            ba_confidence = behavioral.get('confidence', 0)
            scores['behavioral'] = (ba_score + ba_confidence) / 2
            
            # نتيجة الذكاء الاصطناعي
            if ai.get('prediction') != 'HOLD':
                ai_confidence = ai.get('confidence', 0)
                ai_quality = ai.get('prediction_quality', {}).get('overall_quality', 0.5)
                scores['ai'] = (ai_confidence + ai_quality) / 2
            else:
                scores['ai'] = 0.0
            
            return scores
            
        except Exception as e:
            logger.error(f"خطأ في حساب نتائج الطبقات: {e}")
            return {'technical': 0, 'quantitative': 0, 'behavioral': 0, 'ai': 0}
    
    def _calculate_overall_score(self, layer_scores: Dict[str, float]) -> float:
        """حساب النتيجة الإجمالية المرجحة"""
        try:
            weighted_score = 0.0
            total_weight = 0.0
            
            for layer, score in layer_scores.items():
                if layer in self.layer_weights and score > 0:
                    weight = self.layer_weights[layer]
                    weighted_score += score * weight
                    total_weight += weight
            
            return weighted_score / total_weight if total_weight > 0 else 0.0
            
        except Exception:
            return 0.0
    
    def _determine_dominant_direction(self, technical: Dict, quantitative: Dict, 
                                    behavioral: Dict, ai: Dict) -> str:
        """تحديد الاتجاه الغالب"""
        try:
            direction_votes = []
            
            # تصويت التحليل الفني
            ta_direction = technical.get('direction', 'HOLD')
            if ta_direction != 'HOLD':
                direction_votes.append(ta_direction)
            
            # تصويت التحليل الكمي (استنتاج من النتيجة)
            qa_score = quantitative.get('overall_score', 0)
            if qa_score > 0.6:
                direction_votes.append('BULLISH')
            elif qa_score < 0.4:
                direction_votes.append('BEARISH')
            
            # تصويت التحليل السلوكي
            ba_sentiment = behavioral.get('dominant_sentiment', 'NEUTRAL')
            if ba_sentiment in ['GREED', 'EXTREME_GREED']:
                direction_votes.append('BULLISH')
            elif ba_sentiment in ['FEAR', 'EXTREME_FEAR']:
                direction_votes.append('BEARISH')
            
            # تصويت الذكاء الاصطناعي
            ai_prediction = ai.get('prediction', 'HOLD')
            if ai_prediction == 'CALL':
                direction_votes.append('BULLISH')
            elif ai_prediction == 'PUT':
                direction_votes.append('BEARISH')
            
            # تحديد الاتجاه الغالب
            if not direction_votes:
                return 'HOLD'
            
            bullish_votes = direction_votes.count('BULLISH')
            bearish_votes = direction_votes.count('BEARISH')
            
            if bullish_votes > bearish_votes:
                return 'BULLISH'
            elif bearish_votes > bullish_votes:
                return 'BEARISH'
            else:
                return 'HOLD'
                
        except Exception:
            return 'HOLD'

    def _calculate_confidence_level(self, layer_scores: Dict[str, float], overall_score: float) -> float:
        """حساب مستوى الثقة الإجمالي"""
        try:
            # الثقة الأساسية من النتيجة الإجمالية
            base_confidence = overall_score

            # مكافأة التوافق بين الطبقات
            active_layers = [score for score in layer_scores.values() if score > 0]
            if len(active_layers) >= 2:
                consistency_bonus = min(len(active_layers) * 0.05, 0.15)
                base_confidence += consistency_bonus

            # مكافأة الجودة العالية
            high_quality_layers = [score for score in layer_scores.values() if score > 0.8]
            if high_quality_layers:
                quality_bonus = len(high_quality_layers) * 0.03
                base_confidence += quality_bonus

            # تطبيق حدود الثقة
            return max(0.0, min(1.0, base_confidence))

        except Exception:
            return 0.0

    def _determine_signal_strength(self, confidence: float) -> str:
        """تحديد قوة الإشارة"""
        if confidence >= self.decision_thresholds['strong_signal']:
            return 'very_strong'
        elif confidence >= self.decision_thresholds['moderate_signal']:
            return 'strong'
        elif confidence >= self.decision_thresholds['weak_signal']:
            return 'moderate'
        else:
            return 'weak'

    def _make_final_decision(self, direction: str, strength: str) -> SignalDecision:
        """اتخاذ القرار النهائي"""
        try:
            if direction == 'HOLD':
                return SignalDecision.HOLD

            if direction == 'BULLISH':
                if strength == 'very_strong':
                    return SignalDecision.STRONG_CALL
                elif strength == 'strong':
                    return SignalDecision.CALL
                else:
                    return SignalDecision.WEAK_CALL

            elif direction == 'BEARISH':
                if strength == 'very_strong':
                    return SignalDecision.STRONG_PUT
                elif strength == 'strong':
                    return SignalDecision.PUT
                else:
                    return SignalDecision.WEAK_PUT

            return SignalDecision.HOLD

        except Exception:
            return SignalDecision.HOLD

    def _determine_optimal_timeframe(self, strength: str, confidence: float) -> int:
        """تحديد الإطار الزمني الأمثل"""
        try:
            # الإطار الزمني الأساسي حسب القوة
            base_timeframe = self.timeframe_settings.get(strength, 180)

            # تعديل حسب مستوى الثقة
            if confidence >= 0.9:
                return min(base_timeframe + 60, 300)  # زيادة للثقة العالية
            elif confidence <= 0.65:
                return max(base_timeframe - 60, 60)   # تقليل للثقة المنخفضة

            return base_timeframe

        except Exception:
            return 180

    def _analyze_market_condition(self, candles: List[Dict], technical: Dict, quantitative: Dict) -> MarketCondition:
        """تحليل حالة السوق"""
        try:
            # تحليل الاتجاه من آخر 10 شموع
            recent_closes = [c['close'] for c in candles[-10:]]
            price_trend = (recent_closes[-1] - recent_closes[0]) / recent_closes[0]

            # تحليل التقلبات
            price_changes = np.diff(recent_closes) / recent_closes[:-1]
            volatility = np.std(price_changes)

            # تحديد حالة السوق
            if volatility > 0.02:
                return MarketCondition.VOLATILE
            elif volatility < 0.005:
                return MarketCondition.STABLE
            elif price_trend > 0.01:
                return MarketCondition.TRENDING_UP
            elif price_trend < -0.01:
                return MarketCondition.TRENDING_DOWN
            else:
                return MarketCondition.SIDEWAYS

        except Exception:
            return MarketCondition.SIDEWAYS

    def _assess_risk_level(self, layer_scores: Dict[str, float], market_condition: MarketCondition) -> str:
        """تقييم مستوى المخاطر"""
        try:
            # المخاطر الأساسية من جودة التحليل
            avg_score = np.mean(list(layer_scores.values()))

            if avg_score >= 0.8:
                base_risk = 'low'
            elif avg_score >= 0.6:
                base_risk = 'medium'
            else:
                base_risk = 'high'

            # تعديل حسب حالة السوق
            if market_condition == MarketCondition.VOLATILE:
                if base_risk == 'low':
                    return 'medium'
                else:
                    return 'high'
            elif market_condition == MarketCondition.STABLE:
                if base_risk == 'high':
                    return 'medium'
                else:
                    return base_risk

            return base_risk

        except Exception:
            return 'medium'

    def _apply_market_filters(self, signal: TradingSignal, candles: List[Dict]) -> bool:
        """تطبيق فلاتر السوق"""
        try:
            # فلتر التقلبات
            recent_prices = [c['close'] for c in candles[-5:]]
            volatility = np.std(np.diff(recent_prices) / recent_prices[:-1])

            if volatility < self.market_filters['min_volatility']:
                logger.debug("تم رفض الإشارة: تقلبات منخفضة جداً")
                return False

            if volatility > self.market_filters['max_volatility']:
                logger.debug("تم رفض الإشارة: تقلبات عالية جداً")
                return False

            # فلتر الحجم (إذا توفر)
            if 'volume' in candles[-1] and candles[-1]['volume'] > 0:
                recent_volumes = [c.get('volume', 0) for c in candles[-5:]]
                avg_volume = np.mean(recent_volumes[:-1])

                if avg_volume > 0:
                    volume_ratio = candles[-1]['volume'] / avg_volume
                    if volume_ratio < self.market_filters['min_volume_ratio']:
                        logger.debug("تم رفض الإشارة: حجم منخفض")
                        return False

            # فلتر مستوى المخاطر
            if signal.risk_level == 'high' and signal.confidence < 0.8:
                logger.debug("تم رفض الإشارة: مخاطر عالية مع ثقة منخفضة")
                return False

            return True

        except Exception as e:
            logger.error(f"خطأ في تطبيق فلاتر السوق: {e}")
            return False

    def _update_performance_stats(self, signal: TradingSignal):
        """تحديث إحصائيات الأداء"""
        try:
            self.performance_stats['total_signals'] += 1

            # تحديث متوسط الثقة
            total = self.performance_stats['total_signals']
            current_avg = self.performance_stats['avg_confidence']
            new_avg = ((current_avg * (total - 1)) + signal.confidence) / total
            self.performance_stats['avg_confidence'] = new_avg

            # تحديث مساهمات الطبقات
            layer_scores = signal.analysis_breakdown.get('layer_scores', {})
            for layer, score in layer_scores.items():
                if layer in self.performance_stats['layer_contributions']:
                    current_contrib = self.performance_stats['layer_contributions'][layer]
                    new_contrib = ((current_contrib * (total - 1)) + score) / total
                    self.performance_stats['layer_contributions'][layer] = new_contrib

        except Exception as e:
            logger.error(f"خطأ في تحديث الإحصائيات: {e}")

    def update_signal_result(self, signal_id: str, was_successful: bool):
        """تحديث نتيجة الإشارة"""
        try:
            if was_successful:
                self.performance_stats['successful_signals'] += 1
            else:
                self.performance_stats['failed_signals'] += 1

            # حساب معدل النجاح
            total_completed = (self.performance_stats['successful_signals'] +
                             self.performance_stats['failed_signals'])

            if total_completed > 0:
                self.performance_stats['win_rate'] = (
                    self.performance_stats['successful_signals'] / total_completed
                )

        except Exception as e:
            logger.error(f"خطأ في تحديث نتيجة الإشارة: {e}")

    def get_performance_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص الأداء"""
        return {
            'performance_stats': self.performance_stats.copy(),
            'layer_weights': self.layer_weights.copy(),
            'decision_thresholds': self.decision_thresholds.copy(),
            'last_updated': datetime.now().isoformat()
        }

    def adjust_layer_weights(self, performance_data: Dict[str, float]):
        """تعديل أوزان الطبقات بناءً على الأداء"""
        try:
            # تعديل الأوزان بناءً على أداء كل طبقة
            total_adjustment = 0.0

            for layer, performance in performance_data.items():
                if layer in self.layer_weights:
                    # زيادة وزن الطبقات عالية الأداء
                    if performance > 0.7:
                        adjustment = 0.02
                    elif performance < 0.5:
                        adjustment = -0.02
                    else:
                        adjustment = 0.0

                    self.layer_weights[layer] += adjustment
                    total_adjustment += adjustment

            # تطبيع الأوزان
            total_weight = sum(self.layer_weights.values())
            if total_weight > 0:
                for layer in self.layer_weights:
                    self.layer_weights[layer] /= total_weight

            logger.info(f"تم تعديل أوزان الطبقات: {self.layer_weights}")

        except Exception as e:
            logger.error(f"خطأ في تعديل أوزان الطبقات: {e}")

    def is_trading_time_suitable(self) -> bool:
        """التحقق من مناسبة وقت التداول"""
        try:
            current_time = datetime.now()
            current_hour = current_time.hour
            current_minute = current_time.minute

            # تجنب أول وآخر 5 دقائق من كل ساعة
            if current_minute < 5 or current_minute > 55:
                return False

            # تجنب ساعات السوق الهادئة (حسب التوقيت المحلي)
            quiet_hours = [0, 1, 2, 3, 4, 5, 6]  # يمكن تعديلها حسب المنطقة الزمنية
            if current_hour in quiet_hours:
                return False

            return True

        except Exception:
            return True  # افتراضي: السماح بالتداول
